import { describe, it, expect, beforeAll } from 'vitest';
import { 
  EmbeddingModel, 
  createSearchIndex, 
  findBestMatch 
} from './semantic-similarity-search';

interface SearchItem {
  text: string;
  vector: number[];
}

const testData = [
  { input: "Saint‑Anicet Canton de Godmanchester", target: "Saint‑Anicet" },
  { input: "Saguenay arrondissement Chicoutimi",     target: "Saguenay" },
  { input: "Montréal Ville‑Marie",                   target: "Montréal" },
  { input: "Québec Sainte‑Foy–Sillery–Cap‑Rouge",    target: "Québec" },
  { input: "Laval‑des‑Rapides",                      target: "Laval" },
  { input: "St‑Anicet",                              target: "Saint‑Anicet" },
  { input: "MTL",                                    target: "Montréal" },
  { input: "Sherb.",                                 target: "Sherbrooke" },
  { input: "QC",                                     target: "Québec" },
  { input: "T‑R",                                    target: "Trois‑Rivières" },
  { input: "Gat.",                                   target: "Gatineau" },
  { input: "<PERSON>.",                                  target: "<PERSON><PERSON><PERSON>" },
  { input: "St-Jean-sur-Richelieu",                  target: "Saint-Jean-sur-Richelieu" },
  { input: "St-Jean",                                target: "Saint-Jean-sur-Richelieu" },
  { input: "SJSR",                                   target: "Saint-Jean-sur-Richelieu" },
  { input: "St-Hyacinthe",                           target: "Saint-Hyacinthe" },
  { input: "Drummond.",                              target: "Drummondville" },
  { input: "TR",                                     target: "Trois‑Rivières" },
  { input: "Magog près du lac Memphrémagog",         target: "Magog" },
  { input: "Granby Zoo",                             target: "Granby" },
  { input: "Coaticook gorge",                        target: "Coaticook" },
  { input: "Ski Bromont",                            target: "Bromont" },
  { input: "Cowansville Brome-Missisquoi",           target: "Cowansville" },
  { input: "Mont Orford",                            target: "Orford" },
  { input: "Sherbrooke Estrie",                      target: "Sherbrooke" }
];

const targetCorpus = [...new Set(testData.map(item => item.target))];

describe('Semantic Similarity Search', () => {
  let embeddingModel: EmbeddingModel;
  let searchIndex: SearchItem[];

  beforeAll(async () => {
    embeddingModel = new EmbeddingModel();
    await embeddingModel.loadModel(); 
    console.log("Creating search index for tests...");
    searchIndex = await createSearchIndex(targetCorpus, embeddingModel);
    console.log("Search index created.");
  }, 60000); 

  it.each(testData)('should find "$target" for input "$input"', async ({ input, target }) => {
    console.log(`Testing input: "${input}" -> Expected target: "${target}"`);
    
    const queryVector = await embeddingModel.getEmbedding(input);
    
    const result = await findBestMatch(input, searchIndex, embeddingModel);

    console.log(`Found: ${result?.item?.text} (Similarity: ${result?.similarity}, Source: ${result?.source})`);

    expect(result).not.toBeNull();
    expect(result?.item).not.toBeNull();
    expect(result?.item?.text).toBe(target);
  }, 30000); 
});
