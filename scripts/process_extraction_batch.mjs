// ESM version of the process_extraction_batch script
import { chromium } from "playwright";
import { createClient } from '@supabase/supabase-js';
import { rename, readdir, mkdir, access } from "fs/promises";
import path from 'path';
import { fileURLToPath } from 'url';
import * as fs from 'fs/promises';
import { fileTypeFromFile } from 'file-type';
import { PDFDocument } from 'pdf-lib';

// Get the current file's directory
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// These should be environment variables in production
const SUPABASE_URL = "https://sqzqvxqcybghcgrpubsy.supabase.co";
const SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNxenF2eHFjeWJnaGNncnB1YnN5Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0MTA5NTUxOCwiZXhwIjoyMDU2NjcxNTE4fQ.YN8lGh9PurkS8dq7a7zzlrgoETGyWltcJrLnOBI7t6M";
const USER_CODE = "30F3315";
const PASSWORD = "Sainte-Clara1504!";

// Constants
const BASE_URL = "https://www.registrefoncier.gouv.qc.ca/Sirf/";
const ACTE_SEARCH_URL = "https://www.registrefoncier.gouv.qc.ca/Sirf/Script/13_01_11/pf_13_01_11_08_reqst.asp";
const INDEX_SEARCH_URL = "https://www.registrefoncier.gouv.qc.ca/Sirf/Script/13_01_11/pf_13_01_11_02_indx_immbl.asp";
const BASE_DOWNLOAD_DIR = path.join(__dirname, "downloads");

class ExtractionBatchProcessor {
  constructor() {
    // Initialize Supabase client
    this.supabase = createClient(SUPABASE_URL, SUPABASE_KEY);
  }

  /**
   * Main method to run the batch processor
   */
  async run() {
    console.log("Starting extraction batch processor...");
    let browser = null;
    
    try {
      // Launch the browser
      browser = await chromium.launch({
        headless: true, // Set to false for debugging
        downloadsPath: BASE_DOWNLOAD_DIR, // Set global downloads path
        args: ['--disable-dev-shm-usage', '--no-sandbox'], // Add these args for stability
        timeout: 120000 // Increase browser launch timeout to 2 minutes
      });
      
      const page = await browser.newPage();
      
      // Initialize directories
      await this.initDirectories();
      
      // Perform login (one time)
      console.log("Logging in to the website...");
      await this.loginToWebsite(page);
      
      // Process queue items
      let keepProcessing = true;
      let processedCount = 0;
      
      while (keepProcessing) {
        // Get next pending item
        const queueItem = await this.getNextPendingItem();
        
        if (!queueItem) {
          console.log("No more pending items to process.");
          break;
        }
        
        console.log(`Processing item ${queueItem.id} (${queueItem.document_source}: ${queueItem.document_number})...`);
        
        // Update item to Processing
        await this.updateItemStatus(queueItem.id, {
          status: 'Processing',
          processing_started_at: new Date().toISOString()
        });
        
        try {
          // Process this specific item
          const itemDownloadDir = path.join(BASE_DOWNLOAD_DIR, queueItem.id);
          await mkdir(itemDownloadDir, { recursive: true });
          
          // Extract document based on its type
          let filePath;
          if (queueItem.document_source === 'acte') {
            filePath = await this.extractActeDocument(
              page,
              queueItem.document_number,
              itemDownloadDir,
              queueItem.circonscription_fonciere,
              queueItem.acte_type,
              queueItem.id // Pass queueItem.id
            );
          } else {
            filePath = await this.extractIndexDocument(
              page,
              queueItem.document_number,
              itemDownloadDir,
              queueItem.circonscription_fonciere,
              queueItem.cadastre,
              queueItem.designation_secondaire,
              queueItem.id // Pass queueItem.id
            );
          }
          
          // Update item with success
          await this.updateItemStatus(queueItem.id, {
            status: 'Downloaded',
            local_file_path: filePath
          });
          
          console.log(`Successfully downloaded document for item ${queueItem.id}`);
          
          // Increment counter
          processedCount++;
          
          // Optional: limit batch size
          if (processedCount >= 10) {
            console.log("Reached batch limit. Exiting processing loop.");
            keepProcessing = false;
          }
          
          // Add a small delay to avoid overwhelming the site
          await page.waitForTimeout(2000 + Math.random() * 3000);
          
        } catch (error) {
          console.error(`Error processing item ${queueItem.id}:`, error);
          
          // Update retry count and status
          const newRetryCount = (queueItem.retry_count || 0) + 1;
          
          await this.updateItemStatus(queueItem.id, {
            retry_count: newRetryCount,
            error_message: error.message,
            // The status will be handled by the DB trigger, but we'll set it to Pending if retry < 3
            status: newRetryCount >= 3 ? 'Document not Found' : 'Pending'
          });
        }
      }
      
      // Log out if needed
      await this.logout(page);
      
    } catch (error) {
      console.error("Critical error in batch processor:", error);
      throw error;
    } finally {
      await browser.close();
      console.log("Browser closed. Extraction batch complete.");
    }
  }
  
  /**
   * Initialize download directories
   */
  async initDirectories() {
    await mkdir(BASE_DOWNLOAD_DIR, { recursive: true });
  }
  
  /**
   * Log in to the website
   */
  async loginToWebsite(page) {
    try {
      await page.goto(BASE_URL);
      await page.getByRole('link', { name: 'Entrée du site' }).nth(1).click();
      await page.getByRole('textbox', { name: 'Code d\'utilisateur' }).click();
      await page.getByRole('textbox', { name: 'Code d\'utilisateur' }).fill(USER_CODE);
      await page.getByRole('textbox', { name: 'Mot de passe' }).click();
      await page.getByRole('textbox', { name: 'Mot de passe' }).fill(PASSWORD);
      await page.getByRole('button', { name: 'Soumettre' }).click();
      
      // Wait for login to complete
      await page.waitForTimeout(2000);
      
      // Check if login was successful
      const loginError = await page.getByText('Code invalide').isVisible();
      if (loginError) {
        throw new Error("Login failed: Invalid credentials");
      }
      
      console.log("Login successful");
    } catch (error) {
      console.error("Login error:", error);
      throw new Error(`Failed to log in: ${error.message}`);
    }
  }
  
  /**
   * Log out from the website
   */
  async logout(page) {
    try {
      // Add logout steps if the website has a logout function
      // await page.getByRole('link', { name: 'Déconnexion' }).click();
      console.log("Logout complete (or not needed)");
    } catch (error) {
      console.error("Logout error:", error);
      // Don't throw, as this isn't critical
    }
  }
  
  /**
   * Get the next pending item from the queue
   */
  async getNextPendingItem() {
    try {
      const { data, error } = await this.supabase
        .from('extraction_queue')
        .select('*')
        .eq('status', 'Pending')
        .order('created_at', { ascending: true })
        .limit(1)
        .single();
      
      if (error) throw error;
      return data;
    } catch (error) {
      if (error.code === 'PGRST116') {
        // No rows returned
        return null;
      }
      console.error("Error fetching next pending item:", error);
      throw error;
    }
  }
  
  /**
   * Update an item's status in the queue
   */
  async updateItemStatus(itemId, updates) {
    try {
      const { error } = await this.supabase
        .from('extraction_queue')
        .update(updates)
        .eq('id', itemId);
      
      if (error) throw error;
    } catch (error) {
      console.error(`Error updating status for item ${itemId}:`, error);
      throw error;
    }
  }
  
  /**
   * Navigate directly to the appropriate search form URL
   */
  async navigateToSearchForm(page, documentSource) {
    try {
      // Check if we need to log in
      let isLoggedIn = false;
      try {
        // Try to get current URL to see if we've navigated already
        const currentUrl = page.url();
        isLoggedIn = currentUrl.includes('registrefoncier.gouv.qc.ca/Sirf') && 
                     !currentUrl.includes('wfe_accueil_fr.htm') &&
                     !currentUrl.includes('wfe_identif.htm');
      } catch (e) {
        // If error, assume not logged in
        isLoggedIn = false;
      }
      
      if (!isLoggedIn) {
        console.log('Need to log in first...');
        await this.loginToWebsite(page);
        await page.waitForTimeout(2000);
      }
      
      // Navigate directly to the appropriate URL based on document type
      if (documentSource === 'acte') {
        console.log('Navigating directly to acte search URL...');
        await page.goto(ACTE_SEARCH_URL);
      } else {
        console.log('Navigating directly to index search URL...');
        await page.goto(INDEX_SEARCH_URL);
      }
      
      await page.waitForTimeout(3000);
      console.log('Successfully navigated to search form');
    } catch (error) {
      console.error(`Error navigating to search form for ${documentSource}:`, error);
      throw error;
    }
  }
  
  /**
   * Extract an acte document
   */
  async extractActeDocument(
    page,
    inscriptionNumber,
    downloadDir,
    circonscription,
    acteType,
    queueItemId // Add queueItemId parameter
  ) {
    let printButton; // Declare printButton at the top of the method scope
    try {
      const finalActeType = acteType || 'Acte';
      
      // Navigate directly to the acte search page
      await this.navigateToSearchForm(page, 'acte');
      
      // Wait for form page to load completely
      await page.waitForTimeout(3000);
      
      // Handle circonscription selection if provided
      if (circonscription) {
        console.log(`Selecting circonscription: ${circonscription}`);
        const selectElement = page.locator('#selCircnFoncr');
        await selectElement.selectOption({ label: circonscription });
      }
      
      // Handle acte type selection using XPath provided by user
      console.log(`Selecting document type: ${finalActeType}`);
      const typeSelect = page.locator('xpath=/html/body/table[1]/tbody/tr/td[2]/form/table[2]/tbody/tr[7]/td[3]/select');
      await typeSelect.waitFor({ state: 'visible', timeout: 30000 });
      
      switch (finalActeType) {
        case 'Radiation':
          await typeSelect.selectOption('Radiation');
          break;
        case 'Avis d\'adresse':
          await typeSelect.selectOption('Avis d\'adresse');
          break;
        case 'Acte divers':
          await typeSelect.selectOption('Acte divers');
          break;
        case 'Acte':
        default:
          await typeSelect.selectOption('Acte');
          break;
      }
      
      // Enter the inscription number using XPath provided by user
      console.log(`Entering document number: ${inscriptionNumber}`);
      const inscriptionInput = page.locator('xpath=/html/body/table[1]/tbody/tr/td[2]/form/table[2]/tbody/tr[8]/td[3]/input');
      await inscriptionInput.waitFor({ state: 'visible', timeout: 30000 });
      await inscriptionInput.click();
      await inscriptionInput.fill(inscriptionNumber);
      await page.waitForTimeout(2000);
      
      // Note: We're already using the global download path set at browser launch time
      console.log(`Using download directory: ${downloadDir}`);
      
      // Store the current URL before clicking search
      const initialUrl = page.url();
      console.log(`Initial URL before search: ${initialUrl}`);
      
      // Search and download
      console.log("Clicking 'Rechercher'...");
      await page.getByRole('button', { name: 'Rechercher' }).click();
      
      // Wait 3 seconds as requested before checking URL change
      console.log("Waiting 3 seconds before checking URL change...");
      await page.waitForTimeout(3000);
      
      const newUrlAfterClick = page.url();
      console.log(`URL after 'Rechercher' click and 3s wait: ${newUrlAfterClick}`);
      
      if (initialUrl === newUrlAfterClick) {
        console.error("URL did not change after 'Rechercher' click. Assuming search failed or no results page.");
        throw new Error("Search failed: URL did not change after clicking 'Rechercher'.");
      }
      
      // Wait for results page to load by waiting for the main content frame
      console.log("Waiting for acte results page main frame (up to 60 seconds)...");
      try {
        await page.waitForSelector('frame[name="page"]', { timeout: 60000 });
        console.log("Acte results page main frame loaded.");
      } catch (e) {
        console.error("Timeout or error waiting for acte results page main frame:", e);
        throw new Error("Acte results page main frame did not load in time.");
      }
      
      // Check if we got results and try to find and click the print button
      try {
        // Try to access the frames structure. If it fails, likely no results found
        const mainFrameExists = await page.locator('frame[name="page"]').count() > 0;
        if (!mainFrameExists) {
          console.log("Document probably doesn't exist - main frame not found after waiting.");
          throw new Error("Document probably doesn't exist - main frame not found after waiting.");
        }
        
        // Similar approach as in extraire-acte.ts, try to get to the print button directly
        // printButton is already declared at the method's top scope
        try {
          const mainFrame = await page.locator('frame[name="page"]').contentFrame();
          if (!mainFrame) {
            throw new Error("Could not access main frame");
          }
          
          console.log("Looking for navigation frame...");
          const navFrame = await mainFrame.locator('frame[name="frmNavgt"]').contentFrame();
          if (!navFrame) {
            throw new Error("Could not access navigation frame");
          }
          
          console.log("Looking for print button...");
          printButton = navFrame.getByRole('link', { name: 'Imprimer' }); // Assign to higher scoped variable
          
          console.log("Waiting for print button to be visible (up to 60s)...");
          await printButton.waitFor({ state: 'visible', timeout: 60000 }); // Wait for button to be visible
          
          console.log("Print button found and visible.");
          // DO NOT click here yet, click after setting up download listener
        } catch (error) {
          console.log(`Error finding or waiting for print button: ${error.message}`);
          throw new Error(`Failed to find or wait for print button: ${error.message}`);
        }
      } catch (error) {
        console.log(`Navigation or frame error: ${error.message}`);
        throw error;
      }

      if (!printButton) {
        throw new Error("Print button was not located due to an earlier error.");
      }
      
      // Wait for download to complete using Playwright's download event
      console.log("Waiting for download to start...");
      // Initiate download listener first
      const downloadPromise = page.waitForEvent('download', { timeout: 30000 }); 
      
      // Then click the button that triggers the download
      console.log("Clicking print button to trigger download...");
      await printButton.click(); 
      
      const download = await downloadPromise;
      console.log(`Download started: ${download.suggestedFilename()}`);
      
      // Wait for the download to complete and get the path
      // The path() method saves the file to a temporary location if it's not already saved
      // We want it in our BASE_DOWNLOAD_DIR, but Playwright handles this if downloadsPath is set at browser launch
      const downloadedPlaywrightPath = await download.path(); 
      
      if (!downloadedPlaywrightPath) {
        throw new Error("Playwright reported download but path is missing.");
      }
      console.log(`File downloaded by Playwright to: ${downloadedPlaywrightPath}`);

      // The file should be in BASE_DOWNLOAD_DIR. We need to move it to itemDownloadDir.
      // The filename from Playwright might be different from what's on disk if there are conflicts.
      // Let's ensure the file from download.path() is the one we use.
      // We'll construct the expected name in itemDownloadDir for the temporary file.
      const originalDownloadedFileName = path.basename(downloadedPlaywrightPath);
      
      // Rename the downloaded file (from Playwright's temp/cache or BASE_DOWNLOAD_DIR) to a temporary name in itemDownloadDir
      const tempFileName = `${queueItemId}_temp_download${path.extname(originalDownloadedFileName) || ''}`;
      const tempPath = path.join(downloadDir, tempFileName);
      
      // Move the file reported by Playwright to our item-specific temp path
      try {
        await fs.rename(downloadedPlaywrightPath, tempPath);
        console.log(`Document moved from Playwright path to temporary item path: ${tempPath}`);
      } catch (renameError) {
        console.error(`Error moving downloaded file from ${downloadedPlaywrightPath} to temporary path ${tempPath}:`, renameError);
        // Attempt to clean up the original Playwright download if the move fails
        try {
          await fs.unlink(downloadedPlaywrightPath);
          console.log(`Cleaned up original Playwright download: ${downloadedPlaywrightPath}`);
        } catch (unlinkError) {
          console.error(`Failed to clean up original Playwright download ${downloadedPlaywrightPath}:`, unlinkError);
        }
        throw new Error(`Failed to move downloaded file to temp path: ${renameError.message}`);
      }
      
      // Check actual file type and attempt conversion if necessary
      const fileTypeResult = await fileTypeFromFile(tempPath);
      let actualExt = fileTypeResult ? `.${fileTypeResult.ext}` : path.extname(tempPath);
      console.log(`Detected file type for ${tempPath}: ${fileTypeResult?.mime}, extension: ${actualExt}`);

      if (actualExt.toLowerCase() !== '.pdf') {
        console.warn(`Downloaded file ${tempPath} for ${queueItemId} is not a PDF. Actual extension: ${actualExt}. Attempting conversion.`);
        const conversionSuccess = await this.convertToPdfIfNecessary(tempPath, actualExt);
        if (conversionSuccess) {
          console.log(`Successfully converted ${tempPath} to PDF.`);
          // tempPath might now be a .pdf file if conversion happened in-place or replaced it
          // For simplicity, we'll assume convertToPdfIfNecessary replaces tempPath with a PDF version
          // or ensures it's a PDF. The final rename will still target .pdf.
        } else {
          console.error(`Failed to convert ${tempPath} to PDF. Proceeding with renaming, but it might not be a valid PDF.`);
        }
      }

      // Now, rename the temporary file to the final target name, ensuring .pdf extension
      let finalTargetPath = path.join(downloadDir, `${inscriptionNumber}.pdf`);
      
      // Check if final target path already exists
      try {
        await fs.access(finalTargetPath);
        // File exists, create a new name
        const timestamp = Date.now();
        const ext = path.extname(finalTargetPath);
        const baseName = path.basename(finalTargetPath, ext);
        finalTargetPath = path.join(downloadDir, `${baseName}_${timestamp}${ext}`);
        console.warn(`Target file already exists. Saving as: ${finalTargetPath}`);
      } catch (accessError) {
        // File does not exist, proceed with original name
      }

      try {
        await rename(tempPath, finalTargetPath);
        console.log(`Document downloaded and renamed to: ${finalTargetPath}`);
      } catch (renameError) {
        console.error(`Error renaming temporary file ${tempPath} to ${finalTargetPath}:`, renameError);
        // Attempt to clean up the temporary file if the final rename fails
        try {
          await fs.unlink(tempPath);
          console.log(`Cleaned up temporary file: ${tempPath}`);
        } catch (unlinkError) {
          console.error(`Failed to clean up temporary file ${tempPath} after rename error:`, unlinkError);
        }
        throw new Error(`Failed to rename temp file to final path. Temp file was: ${tempPath}. Error: ${renameError.message}`);
      }
      
      // Verify the file exists after renaming
      try {
        await fs.access(finalTargetPath);
        return finalTargetPath;
      } catch (error) {
        // This error is critical if the file doesn't exist after a successful rename log
        console.error(`CRITICAL: File ${finalTargetPath} not accessible after reported successful rename.`);
        throw new Error(`File was reportedly renamed but couldn't be accessed: ${error.message}`);
      }
    } catch (error) {
      console.error("Error extracting acte document:", error);
      throw error;
    }
  }
  
  /**
   * Extract an index document
   */
  async extractIndexDocument(
    page,
    lotNumber,
    downloadDir,
    circonscription,
    cadastre,
    designationSecondaire,
    queueItemId // Add queueItemId parameter
  ) {
    let printButton; // Declare printButton at the top of the method scope
    try {
      // Navigate to the index search page using popup approach
      await this.navigateToSearchForm(page, 'index');
      
      // Wait for form page to load completely
      await page.waitForTimeout(3000);
      
      // Handle circonscription selection if provided
      if (circonscription) {
        console.log(`Selecting circonscription: ${circonscription}`);
        const selectElement = page.locator('#selCircnFoncr');
        await selectElement.selectOption({ label: circonscription });
      }
      
      // Fill in cadastre if provided
      if (cadastre) {
        console.log(`Selecting cadastre: ${cadastre}`);
        const cadastreSelect = page.getByLabel('Cadastre');
        await cadastreSelect.waitFor({ state: 'visible', timeout: 30000 });
        await cadastreSelect.selectOption({ label: cadastre });
      }
      
      // Enter the lot number
      console.log(`Entering lot number: ${lotNumber}`);
      const lotInput = page.getByRole('textbox', { name: 'Numéro de lot' });
      await lotInput.waitFor({ state: 'visible', timeout: 30000 });
      await lotInput.click();
      await lotInput.fill(lotNumber);
      await page.waitForTimeout(1000); // Shorter timeout after lot number

      // Fill in designation secondaire if provided (as the last step before submit)
      if (designationSecondaire) {
        console.log(`Selecting designation secondaire: ${designationSecondaire}`);
        const designationInput = page.getByLabel('Désignation secondaire');
        await designationInput.waitFor({ state: 'visible', timeout: 30000 });
        await designationInput.selectOption({ label: designationSecondaire });
        await page.waitForTimeout(1000); // Shorter timeout after designation
      }
      
      // Note: We're already using the global download path set at browser launch time
      console.log(`Using download directory: ${downloadDir}`);
      
      // Search and download
      console.log("Clicking 'Soumettre'...");
      const initialIndexUrl = page.url();
      await page.getByRole('button', { name: 'Soumettre' }).click();

      // Wait 3 seconds as requested before checking URL change
      console.log("Waiting 3 seconds before checking URL change...");
      await page.waitForTimeout(3000);

      const newUrlAfterClickIndex = page.url();
      console.log(`URL after 'Soumettre' click and 3s wait: ${newUrlAfterClickIndex}`);

      if (initialIndexUrl === newUrlAfterClickIndex) {
        console.error("URL did not change after 'Soumettre' click. Assuming search failed or no results page.");
        throw new Error("Search failed: URL did not change after clicking 'Soumettre'.");
      }
      
      // Wait for results page to load by waiting for the main content frame
      console.log("Waiting for index results page main frame (up to 60 seconds)...");
      try {
        await page.waitForSelector('frame[name="page"]', { timeout: 60000 });
        console.log("Index results page main frame loaded.");
      } catch (e) {
        console.error("Timeout or error waiting for index results page main frame:", e);
        throw new Error("Index results page main frame did not load in time.");
      }
      
      // First, check if we got results
      try {
        // Try to access the frames structure. If it fails, likely no results found
        const mainFrameExists = await page.locator('frame[name="page"]').count() > 0;
        if (!mainFrameExists) {
          console.log("Document probably doesn't exist - main frame not found after waiting.");
          throw new Error("Document probably doesn't exist - main frame not found after waiting.");
        }
        
        // Similar approach as in extraite-index.ts, try to get to the print button directly
        // printButton is already declared at the method's top scope
        try {
          const mainFrame = await page.locator('frame[name="page"]').contentFrame();
          if (!mainFrame) {
            throw new Error("Could not access main frame");
          }
          
          console.log("Looking for navigation frame...");
          const navFrame = await mainFrame.locator('frame[name="frmNavgt"]').contentFrame();
          if (!navFrame) {
            throw new Error("Could not access navigation frame");
          }
          
          console.log("Looking for print button...");
          printButton = navFrame.getByRole('link', { name: 'Imprimer' }); // Assign to higher scoped variable

          console.log("Waiting for print button to be visible (up to 60s)...");
          await printButton.waitFor({ state: 'visible', timeout: 60000 }); // Wait for button to be visible

          console.log("Print button found and visible.");
           // DO NOT click here yet, click after setting up download listener
        } catch (error) {
          console.log(`Error finding or waiting for print button: ${error.message}`);
          throw new Error(`Failed to find or wait for print button: ${error.message}`);
        }
      } catch (error) {
        console.log(`Navigation or frame error: ${error.message}`);
        throw error;
      }

      if (!printButton) {
        throw new Error("Print button was not located due to an earlier error.");
      }
      
      // Wait for download to complete using Playwright's download event
      console.log("Waiting for download to start...");
      // Initiate download listener first
      const downloadPromise = page.waitForEvent('download', { timeout: 30000 });

      // Then click the button that triggers the download
      console.log("Clicking print button to trigger download...");
      await printButton.click();

      const download = await downloadPromise;
      console.log(`Download started: ${download.suggestedFilename()}`);

      // Wait for the download to complete and get the path
      const downloadedPlaywrightPath = await download.path();

      if (!downloadedPlaywrightPath) {
        throw new Error("Playwright reported download but path is missing.");
      }
      console.log(`File downloaded by Playwright to: ${downloadedPlaywrightPath}`);

      // The file should be in BASE_DOWNLOAD_DIR. We need to move it to itemDownloadDir.
      const originalDownloadedFileName = path.basename(downloadedPlaywrightPath);

      // Rename the downloaded file (from Playwright's temp/cache or BASE_DOWNLOAD_DIR) to a temporary name in itemDownloadDir
      const tempFileName = `${queueItemId}_temp_download${path.extname(originalDownloadedFileName) || ''}`;
      const tempPath = path.join(downloadDir, tempFileName);
      
      // Move the file reported by Playwright to our item-specific temp path
      try {
        await fs.rename(downloadedPlaywrightPath, tempPath);
        console.log(`Document moved from Playwright path to temporary item path: ${tempPath}`);
      } catch (renameError) {
        console.error(`Error moving downloaded file from ${downloadedPlaywrightPath} to temporary path ${tempPath}:`, renameError);
        // Attempt to clean up the original Playwright download if the move fails
        try {
          await fs.unlink(downloadedPlaywrightPath);
          console.log(`Cleaned up original Playwright download: ${downloadedPlaywrightPath}`);
        } catch (unlinkError) {
          console.error(`Failed to clean up original Playwright download ${downloadedPlaywrightPath}:`, unlinkError);
        }
        throw new Error(`Failed to move downloaded file to temp path: ${renameError.message}`);
      }

      // Check actual file type and attempt conversion if necessary
      const fileTypeResult = await fileTypeFromFile(tempPath);
      let actualExt = fileTypeResult ? `.${fileTypeResult.ext}` : path.extname(tempPath);
      console.log(`Detected file type for ${tempPath}: ${fileTypeResult?.mime}, extension: ${actualExt}`);

      if (actualExt.toLowerCase() !== '.pdf') {
        console.warn(`Downloaded file ${tempPath} for ${queueItemId} is not a PDF. Actual extension: ${actualExt}. Attempting conversion.`);
        const conversionSuccess = await this.convertToPdfIfNecessary(tempPath, actualExt);
        if (conversionSuccess) {
          console.log(`Successfully converted ${tempPath} to PDF.`);
        } else {
          console.error(`Failed to convert ${tempPath} to PDF. Proceeding with renaming, but it might not be a valid PDF.`);
        }
      }

      // Now, rename the temporary file to the final target name, ensuring .pdf extension
      let finalTargetPath = path.join(downloadDir, `${lotNumber}.pdf`);

      // Check if final target path already exists
      try {
        await fs.access(finalTargetPath);
        // File exists, create a new name
        const timestamp = Date.now();
        const ext = path.extname(finalTargetPath);
        const baseName = path.basename(finalTargetPath, ext);
        finalTargetPath = path.join(downloadDir, `${baseName}_${timestamp}${ext}`);
        console.warn(`Target file already exists. Saving as: ${finalTargetPath}`);
      } catch (accessError) {
        // File does not exist, proceed with original name
      }

      try {
        await rename(tempPath, finalTargetPath);
        console.log(`Document downloaded and renamed to: ${finalTargetPath}`);
      } catch (renameError) {
        console.error(`Error renaming temporary file ${tempPath} to ${finalTargetPath}:`, renameError);
        // Attempt to clean up the temporary file if the final rename fails
        try {
          await fs.unlink(tempPath);
          console.log(`Cleaned up temporary file: ${tempPath}`);
        } catch (unlinkError) {
          console.error(`Failed to clean up temporary file ${tempPath} after rename error:`, unlinkError);
        }
        throw new Error(`Failed to rename temp file to final path. Temp file was: ${tempPath}. Error: ${renameError.message}`);
      }
      
      // Verify the file exists after renaming
      try {
        await fs.access(finalTargetPath);
        return finalTargetPath;
      } catch (error) {
        // This error is critical if the file doesn't exist after a successful rename log
        console.error(`CRITICAL: File ${finalTargetPath} not accessible after reported successful rename.`);
        throw new Error(`File was reportedly renamed but couldn't be accessed: ${error.message}`);
      }
    } catch (error) {
      console.error("Error extracting index document:", error);
      throw error;
    }
  }
  
  /**
   * Helper method to find PDF files in the download directory and move them to the item directory
   * @param {string} itemDownloadDir - The target directory to move files to
   * @returns {Promise<boolean>} - True if a file was found and moved, false otherwise
   */
  async findAndMoveDownloadedFile(itemDownloadDir) {
    try {
      console.log("Searching for PDF files in download directory...");
      
      // Get all PDF files in the base download directory
      const baseFiles = await readdir(BASE_DOWNLOAD_DIR);
      let pdfFound = false;
      
      for (const file of baseFiles) {
        if (file.toLowerCase().endsWith('.pdf')) {
          console.log(`Found PDF file in base directory: ${file}`);
          const sourcePath = path.join(BASE_DOWNLOAD_DIR, file);
          const targetPath = path.join(itemDownloadDir, file);
          
          // Check if it's a file
          try {
            const stats = await fs.stat(sourcePath);
            if (stats.isFile()) {
              // Copy the file to the item directory
              await fs.copyFile(sourcePath, targetPath);
              console.log(`Successfully copied PDF file to item directory: ${file}`);
              pdfFound = true;
              break; // We only need one file
            }
          } catch (error) {
            console.log(`Error accessing file ${file}:`, error);
          }
        }
      }
      
      return pdfFound;
    } catch (error) {
      console.error("Error finding PDF files:", error);
      return false;
    }
  }

  // Helper method for PDF conversion (currently supports images)
  async convertToPdfIfNecessary(filePath, originalExtension) {
    try {
      const fileBuffer = await fs.readFile(filePath);
      let pdfBytes;

      if (['.jpg', '.jpeg', '.png'].includes(originalExtension.toLowerCase())) {
        const pdfDoc = await PDFDocument.create();
        let image;
        if (originalExtension.toLowerCase() === '.png') {
          image = await pdfDoc.embedPng(fileBuffer);
        } else { // jpg/jpeg
          image = await pdfDoc.embedJpg(fileBuffer);
        }
        const page = pdfDoc.addPage([image.width, image.height]);
        page.drawImage(image, {
          x: 0,
          y: 0,
          width: image.width,
          height: image.height,
        });
        pdfBytes = await pdfDoc.save();
        await fs.writeFile(filePath, pdfBytes); // Overwrite original with PDF version
        return true;
      } else {
        console.log(`File ${filePath} is not a supported image type for PDF conversion with pdf-lib (ext: ${originalExtension}). Skipping conversion.`);
        return false; // Not a supported image or already PDF (assumed)
      }
    } catch (error) {
      console.error(`Error during PDF conversion for ${filePath}:`, error);
      return false;
    }
  }
}

// Run the processor
const processor = new ExtractionBatchProcessor();
processor.run()
  .then(() => {
    console.log("Batch process completed successfully");
    process.exit(0);
  })
  .catch((error) => {
    console.error("Batch process failed:", error);
    process.exit(1);
  });
