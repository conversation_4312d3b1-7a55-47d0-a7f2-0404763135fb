import { pipeline, Pipeline, FeatureExtractionPipeline, Tensor } from '@xenova/transformers';
import { aliasMap } from './alias-map';

declare global {
  interface Window {
    transformers?: {
      pipeline: typeof pipeline;
    };
  }
}

interface SearchItem {
  text: string;
  vector: number[];
}


/**
 * Calculates cosine similarity between two vectors.
 * @param {number[]} v1 - The first vector.
 * @param {number[]} v2 - The second vector.
 * @returns {number} The cosine similarity score between -1 and 1 (1 being identical).
 * @throws {Error} If the vectors have different dimensions.
 */
export function calculateCosineSimilarity(v1: number[], v2: number[]): number {
    if (v1.length !== v2.length) {
      throw new Error("Vectors must have the same dimensions");
    }
    
    let dotProduct = 0;
    let magnitude1 = 0;
    let magnitude2 = 0;
    
    for (let i = 0; i < v1.length; i++) {
      dotProduct += v1[i] * v2[i];
      magnitude1 += v1[i] * v1[i];
      magnitude2 += v2[i] * v2[i];
    }
    
    magnitude1 = Math.sqrt(magnitude1);
    magnitude2 = Math.sqrt(magnitude2);
    
    if (magnitude1 === 0 || magnitude2 === 0) {
      return 0; 
    }
    
    return dotProduct / (magnitude1 * magnitude2);
  }
  
/**
 * Finds the single most similar item to a query vector using cosine similarity.
 * @param {SearchItem[]} items - An array of objects, each with 'text' and 'vector' properties.
 * @param {number[]} queryVector - The vector to compare against the items.
 * @returns {{ item: SearchItem | null, similarity: number } | null} An object containing the most similar item found and its similarity score, or null if no items are provided.
 */
export function findMostSimilarItem(items: SearchItem[], queryVector: number[]): { item: SearchItem | null, similarity: number } | null {
    if (!items || !items.length) {
      return null;
    }
    
    let mostSimilarItem: SearchItem | null = null;
    let highestSimilarity: number = -Infinity;
    
    for (const item of items) {
      const similarity: number = calculateCosineSimilarity(item.vector, queryVector);
      
      if (similarity > highestSimilarity) {
        highestSimilarity = similarity;
        mostSimilarItem = item;
      }
    }
    
    return {
      item: mostSimilarItem,
      similarity: highestSimilarity
    };
  }
  
/**
 * Finds multiple items similar to a query vector, exceeding a given similarity threshold.
 * @param {SearchItem[]} items - An array of objects, each with 'text' and 'vector' properties.
 * @param {number[]} queryVector - The vector to compare against the items.
 * @param {number} [threshold=0.8] - The minimum cosine similarity score for an item to be included in the results.
 * @param {number} [maxResults=10] - The maximum number of similar items to return.
 * @returns {Array<{ item: SearchItem, similarity: number }>} An array of objects, each containing a similar item and its similarity score, sorted by similarity in descending order. Returns an empty array if no items are provided or none meet the threshold.
 */
export function findSimilarItems(
    items: SearchItem[], 
    queryVector: number[], 
    threshold: number = 0.8, 
    maxResults: number = 10
  ): Array<{ item: SearchItem, similarity: number }> {
    if (!items || !items.length) {
      return [];
    }
    
    const results: Array<{ item: SearchItem, similarity: number }> = [];
    
    for (const item of items) {
      const similarity: number = calculateCosineSimilarity(item.vector, queryVector);
      
      if (similarity >= threshold) {
        results.push({ item, similarity });
      }
    }
    
    results.sort((a, b) => b.similarity - a.similarity);
    
    return results.slice(0, maxResults);
  }
  
/**
 * Normalizes a vector to have a unit length (magnitude of 1).
 * This is often important for accurate cosine similarity calculations.
 * @param {number[]} vector - The input vector.
 * @returns {number[]} The normalized vector. Returns a zero vector if the input magnitude is 0.
 */
export function normalizeVector(vector: number[]): number[] {
    const magnitude: number = Math.sqrt(vector.reduce((sum, val) => sum + val * val, 0));
    
    if (magnitude === 0) {
      return new Array(vector.length).fill(0);
    }
    
    return vector.map(val => val / magnitude);
  }
  
/**
 * Manages the loading and usage of a feature-extraction pipeline (embedding model)
 * from the Transformers.js library.
 */
  export class EmbeddingModel {
    private modelLoaded: boolean = false;
    private pipeline: FeatureExtractionPipeline | null = null; 
  
    /**
     * Loads the feature-extraction pipeline model ('Xenova/all-MiniLM-L6-v2').
     * Ensures the model is loaded only once.
     * @returns {Promise<void>} A promise that resolves when the model is loaded.
     * @throws {Error} If the model fails to load.
     */
    async loadModel() {
      if (this.modelLoaded) return;
      
      try {
        const { pipeline } = await import('@xenova/transformers');
        
        console.log("Attempting to load pipeline 'feature-extraction' with model 'Xenova/all-MiniLM-L6-v2'...");
        this.pipeline = await pipeline('feature-extraction', 'Xenova/all-MiniLM-L6-v2');
        this.modelLoaded = true;
        console.log('Model pipeline loaded successfully.');
      } catch (error) {
        console.error('Detailed error loading model pipeline:', error); 
        throw new Error(`Failed to load the embedding model. Reason: ${error instanceof Error ? error.message : String(error)}`);
      }
    }
  
    /**
     * Generates an embedding vector for the given text using the loaded model.
     * Loads the model if it hasn't been loaded yet.
     * @param {string} text - The input text string to embed.
     * @returns {Promise<number[]>} A promise that resolves with the embedding vector (an array of numbers).
     * @throws {Error} If the pipeline is not loaded or embedding generation fails.
     */
    async getEmbedding(text: string): Promise<number[]> {
      if (!this.modelLoaded || !this.pipeline) {
        await this.loadModel();
      }
  
      if (!this.pipeline) {
        throw new Error('Pipeline not loaded');
      }

      try {
        const result: Tensor = await this.pipeline(text, { pooling: 'mean', normalize: true });
        
        if (result && result.data) {
          return Array.from(result.data as Float32Array | Int32Array | Uint8Array); 
        } else {
          console.warn('Embedding result structure might be unexpected:', result);
          return []; 
        }
      } catch (error) {
        console.error('Error generating embedding:', error);
        throw new Error('Failed to generate embedding');
      }
    }
  }
  
/**
 * Creates a search index by generating embeddings for a list of strings.
 * @param {string[]} strings - An array of strings to include in the index.
 * @param {EmbeddingModel} embeddingModel - An instance of the EmbeddingModel class, used to generate vectors.
 * @returns {Promise<SearchItem[]>} A promise that resolves with an array of SearchItem objects, each containing the original text and its corresponding embedding vector. Logs errors for strings that fail to embed.
 */
export async function createSearchIndex(strings: string[], embeddingModel: EmbeddingModel): Promise<SearchItem[]> {
    const items: SearchItem[] = [];
    
    for (const text of strings) {
      try {
        const vector: number[] = await embeddingModel.getEmbedding(text);
        items.push({ text, vector }); 
      } catch (error) {
        console.error(`Error embedding text: "${text}"`, error);
      }
    }
    
    return items;
  }
  
/**
 * Finds the best match for a query text by first checking a predefined alias map
 * and then falling back to semantic similarity search if no alias is found.
 * @param {string} queryText - The input text to search for.
 * @param {SearchItem[]} searchIndex - The pre-computed search index (array of SearchItem objects).
 * @param {EmbeddingModel} embeddingModel - An instance of the EmbeddingModel class for generating embeddings if needed.
 * @returns {Promise<{ item: SearchItem | null, similarity: number, source: 'alias' | 'semantic' } | null>} A promise that resolves with an object containing the best matching item, its similarity score (1.0 for alias matches), and the source ('alias' or 'semantic'), or null if no match is found.
 */
export async function findBestMatch(
  queryText: string,
  searchIndex: SearchItem[],
  embeddingModel: EmbeddingModel
): Promise<{ item: SearchItem | null, similarity: number, source: 'alias' | 'semantic' } | null> {
  
  const aliasTarget = aliasMap[queryText];
  if (aliasTarget) {
    const aliasedItem = searchIndex.find(item => item.text === aliasTarget);
    if (aliasedItem) {
      console.log(`Match found via alias: "${queryText}" -> "${aliasTarget}"`);
      return { item: aliasedItem, similarity: 1.0, source: 'alias' }; 
    } else {
      console.warn(`Alias target "${aliasTarget}" not found in search index.`);
    }
  }

  console.log(`No alias found for "${queryText}", performing semantic search...`);
  const queryVector = await embeddingModel.getEmbedding(queryText);
  const semanticResult = findMostSimilarItem(searchIndex, queryVector);

  if (semanticResult && semanticResult.item) {
    return { ...semanticResult, source: 'semantic' };
  }

  return null; 
}
