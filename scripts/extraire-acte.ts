import { chromium } from "playwright";
import { rename, readdir, readFile, unlink } from "fs/promises";
import { argv } from "process";
import { mkdir } from "fs/promises";
import {
  EmbeddingModel,
  createSearchIndex,
  findBestMatch
} from './semantic-similarity-search';

interface AppConfig {
  targetUrl: string;
  defaultTimeout: number;
  userCode: string;
  password: string;
  lotNumber: string;
  inscriptionNumber: string;
  circonscription?: string;
}

async function loadConfig(): Promise<AppConfig> {
  try {
    const configFile = await readFile("config.json", "utf-8");
    const config = JSON.parse(configFile);
    if (
      typeof config.targetUrl !== "string" ||
      typeof config.defaultTimeout !== "number"
    ) {
      throw new Error("Invalid config.json structure");
    }
    // Add validation for inscriptionNumber
    if (typeof config.inscriptionNumber !== "string") {
      throw new Error("Invalid config.json structure: missing inscriptionNumber");
    }
    return config as AppConfig;
  } catch (error) {
    console.error("Error loading or parsing config.json:", error);
    return {
      targetUrl: "https://example.com",
      defaultTimeout: 30000,
      userCode: "",
      password: "",
      lotNumber: "",
      inscriptionNumber: "", 
      circonscription: "",
    };
  }
}

let downloadPath: string;
let inscriptionNumber: string; 

const main = async () => {
  const config = await loadConfig();
  const { userCode, password, inscriptionNumber: configInscriptionNumber, circonscription  } = config; 
  inscriptionNumber = configInscriptionNumber;

  downloadPath = `./inscription-${inscriptionNumber}`; 
  await mkdir(downloadPath, { recursive: true }); 

  const args = argv.slice(2);
  const isHeaded = args.includes("--headed")

  const browser = await chromium.launch({
    headless: !isHeaded,
    downloadsPath: downloadPath, 
  });
  const page = await browser.newPage();

  page.setDefaultTimeout(config.defaultTimeout);

  await page.goto(config.targetUrl);

  await page.goto('https://www.registrefoncier.gouv.qc.ca/Sirf/');
  await page.getByRole('link', { name: 'Entrée du site' }).nth(1).click();
  await page.getByRole('textbox', { name: 'Code d\'utilisateur' }).click();
  await page.getByRole('textbox', { name: 'Code d\'utilisateur' }).fill(userCode); 
  await page.getByRole('textbox', { name: 'Mot de passe' }).click();
  await page.getByRole('textbox', { name: 'Mot de passe' }).fill(password); 

  await page.getByRole('button', { name: 'Soumettre' }).click();
  const page1Promise = page.waitForEvent('popup');
  await page.getByRole('link', { name: 'Consulter', exact: true }).click();
  const page1 = await page1Promise;
  await page1.getByRole('link', { name: 'Index des immeubles' }).click();

  const page2Promise = page.waitForEvent('popup');
  await page.getByRole('link', { name: 'Consulter', exact: true }).click();
  const page2 = await page2Promise;

  await page2.getByRole('link', { name: 'Acte au long, radiation, avis' }).click();


  await page.waitForTimeout(1000);

  if (circonscription) {
    const options = await page.$$eval('#selCircnFoncr option', (options: HTMLOptionElement[]) => options.map(option => ({
      value: option.value,
      text: option.textContent
    }))
    )

    const embeddingModel = new EmbeddingModel();
    await embeddingModel.loadModel()

    const targetCorpus = [...new Set(options.map(item => item.text!))];

    const searchIndex = await createSearchIndex(targetCorpus, embeddingModel);

    const result = await findBestMatch(circonscription, searchIndex, embeddingModel);


    if (result?.item?.text) {
      const selectElement = page.locator('#selCircnFoncr');
      await selectElement.selectOption(result?.item?.text);
    }
  }

  await page.getByRole('textbox', { name: 'Numéro d\'inscription' }).click();
  await page.getByRole('textbox', { name: 'Numéro d\'inscription' }).fill(inscriptionNumber); 
  await page.waitForTimeout(1000);

  await page.getByRole('button', { name: 'Rechercher' }).click();
  await page.locator('frame[name="page"]').contentFrame().locator('frame[name="frmNavgt"]').contentFrame().getByRole('link', { name: 'Imprimer' }).click();


  // Keep the browser open for a few seconds to observe the navigation
  await page.waitForTimeout(5000);

  await browser.close();
};

main().then(async () => {
  const files = await readdir(downloadPath);
  const file = files[0];
  if (file) {
    // Rename the file to inscriptionNumber.pdf
    await rename(`${downloadPath}/${file}`, `${downloadPath}/${inscriptionNumber}.pdf`);
  }

});
