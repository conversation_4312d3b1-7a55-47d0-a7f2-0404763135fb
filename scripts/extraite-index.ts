import { chromium } from "playwright";
import { rename, readdir, readFile, unlink } from "fs/promises";
import { argv } from "process";
import { mkdir } from "fs/promises";

interface AppConfig {
  targetUrl: string;
  defaultTimeout: number;
  userCode: string;
  password: string;
  lotNumber: string;
}

async function loadConfig(): Promise<AppConfig> {
  try {
    const configFile = await readFile("config.json", "utf-8");
    const config = JSON.parse(configFile);
    if (
      typeof config.targetUrl !== "string" ||
      typeof config.defaultTimeout !== "number"
    ) {
      throw new Error("Invalid config.json structure");
    }
    return config as AppConfig;
  } catch (error) {
    console.error("Error loading or parsing config.json:", error);
    return {
      targetUrl: "https://example.com",
      defaultTimeout: 30000,
      userCode: "",
      password: "",
      lotNumber: "",
    };
  }
}

let downloadPath: string;
let lotNumber: string;

const main = async () => {
  const config = await loadConfig();
  const { userCode, password, lotNumber: configLotNumber } = config;
  lotNumber = configLotNumber;

  downloadPath = `./downloads/${lotNumber}`;
  await mkdir(downloadPath, { recursive: true });

  const args = argv.slice(2);
  const isHeaded = args.includes("--headed");

  const browser = await chromium.launch({
    headless: !isHeaded,
    downloadsPath: downloadPath,
  });
  const page = await browser.newPage();

  page.setDefaultTimeout(config.defaultTimeout);

  await page.goto(config.targetUrl);

  // enter website
  await page.goto("https://www.registrefoncier.gouv.qc.ca/Sirf/");
  await page.getByRole("link", { name: "Entrée du site" }).nth(1).click();

  // login
  await page.getByRole("textbox", { name: "Code d'utilisateur" }).click();
  await page
    .getByRole("textbox", { name: "Code d'utilisateur" })
    .fill(userCode);
  await page.getByRole("textbox", { name: "Mot de passe" }).click();
  await page.getByRole("textbox", { name: "Mot de passe" }).fill(password);
  await page.getByRole("button", { name: "Soumettre" }).click();

  // login will trigger a popup, select the menu we need will close the pop up and renavigate automatically main page
  const page1Promise = page.waitForEvent("popup");
  await page.getByRole("link", { name: "Consulter", exact: true }).click();
  const page1 = await page1Promise;
  await page1.getByRole("link", { name: "Index des immeubles" }).click();

  await page.getByRole("textbox", { name: "Numéro de lot" }).click();
  await page.getByRole("textbox", { name: "Numéro de lot" }).fill(lotNumber);

  await page.getByRole("button", { name: "Soumettre" }).click();
  await page
    .locator('frame[name="page"]')
    .contentFrame()
    .locator('frame[name="frmNavgt"]')
    .contentFrame()
    .getByRole("link", { name: "Imprimer" })
    .click();

  // wait for the download to complete, the page download event is a bit unreliable.
  await page.waitForTimeout(10000);

  await browser.close();
};

main().then(async () => {
  const files = await readdir(downloadPath);
  const file = files[0];
  if (file) {
    await rename(`${downloadPath}/${file}`, `${downloadPath}/${lotNumber}.pdf`);
  }

  // push file to google drive
  // delete file from downloads
});
