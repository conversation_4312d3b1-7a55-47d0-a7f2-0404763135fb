
import { useToast } from '@/hooks/use-toast';
import { prepareBasePayload, sendWebhookRequest } from './base-webhook-utils';

/**
 * Triggers a status webhook with the provided data
 */
export const triggerStatusWebhook = async (
  url: string,
  requestData: any,
  indexData: any,
  toastFn: ReturnType<typeof useToast>['toast']
) => {
  try {
    console.log(`Triggering status webhook:`, url);
    
    // Prepare the data for the status change webhook
    const payload = {
      event_type: 'index_status_ready',
      ...prepareBasePayload(requestData, indexData),
      index_name: indexData?.index_name,
      index_type: indexData?.index_type,
      index_status: indexData?.index_status,
      // Include timestamps
      created_at: indexData?.created_at,
      updated_at: indexData?.updated_at,
      // Include full objects for reference if needed
      request: requestData,
      index: indexData
    };
    
    const success = await sendWebhookRequest(url, 'POST', payload, toastFn);
    
    if (success) {
      toastFn({
        title: 'Notification envoyée',
        description: 'Le webhook de changement de statut a été déclenché avec succès.',
      });
    } else {
      throw new Error('Failed to trigger status webhook');
    }
  } catch (error) {
    console.error('Error triggering status webhook:', error);
    toastFn({
      title: 'Erreur',
      description: 'Nous n\'avons pas pu déclencher le webhook de changement de statut.',
      variant: 'destructive',
    });
  }
};
