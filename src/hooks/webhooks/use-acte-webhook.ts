
import { useState, useEffect } from 'react';
import { useToast } from '@/hooks/use-toast';

export const useActeWebhook = () => {
  const { toast } = useToast();
  const [acteWebhookUrl, setActeWebhookUrl] = useState('');

  useEffect(() => {
    // Load acte webhook URL from localStorage on component mount
    const savedUrl = localStorage.getItem('acteWebhookUrl') || '';
    console.log('Loading acteWebhookUrl from localStorage:', savedUrl);
    setActeWebhookUrl(savedUrl);
  }, []);

  const handleActeWebhookUrlChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const url = e.target.value;
    console.log('Setting acteWebhookUrl:', url);
    setActeWebhookUrl(url);
    localStorage.setItem('acteWebhookUrl', url);
  };

  return {
    acteWebhookUrl,
    handleActeWebhookUrlChange,
  };
};
