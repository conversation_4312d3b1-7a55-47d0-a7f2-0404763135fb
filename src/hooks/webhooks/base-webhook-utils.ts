
import { useToast } from '@/hooks/use-toast';

/**
 * Prepares the common payload for all webhook types
 */
export const prepareBasePayload = (requestData: any, indexData: any) => {
  return {
    request_id: requestData.id,
    index_id: indexData?.id,
    numero_lot: indexData?.lot_number || indexData?.index_data?.property_details?.numero_lot || requestData.numero_lot,
    circonscription_fonciere: indexData?.circonscription || indexData?.index_data?.property_details?.circonscription_fonciere || requestData.circonscription_fonciere,
    paroisse_cadastrale: indexData?.cadastre || indexData?.index_data?.property_details?.paroisse_cadastrale || requestData.paroisse_cadastrale,
  };
};

/**
 * Sends a webhook request with the given configuration
 */
export const sendWebhookRequest = async (
  url: string,
  method: string,
  payload: any,
  toastFn: ReturnType<typeof useToast>['toast']
) => {
  if (!url) {
    console.log('No webhook URL provided, skipping webhook trigger');
    return false;
  }

  try {
    console.log(`Sending ${method} webhook to:`, url);
    console.log('Webhook payload:', payload);

    // Common request options
    const requestOptions: RequestInit = {
      method: method,
      headers: {
        'Content-Type': 'application/json',
      },
    };

    let requestUrl = url;
    
    if (method === 'GET') {
      // For GET requests, append data as query parameters
      const queryParams = new URLSearchParams();
      for (const [key, value] of Object.entries(payload)) {
        if (value !== null && value !== undefined && typeof value !== 'object') {
          queryParams.append(key, String(value));
        }
      }
      
      requestUrl = `${url}${url.includes('?') ? '&' : '?'}${queryParams.toString()}`;
      console.log('GET Request URL:', requestUrl);
    } else {
      // For POST requests, add the payload as body
      requestOptions.body = JSON.stringify(payload);
    }
    
    // Removing 'no-cors' mode to get proper response status
    console.log('Making fetch request to URL:', requestUrl);
    console.log('With options:', JSON.stringify(requestOptions));
    
    // Make the fetch request
    const response = await fetch(requestUrl, requestOptions);
    
    console.log('Webhook response status:', response.status);
    
    // Check if the request was successful
    if (response.ok) {
      console.log('Webhook sent successfully');
      return true;
    } else {
      console.error('Webhook request failed with status:', response.status);
      // Try to get the response text for more detailed error information
      try {
        const responseText = await response.text();
        console.error('Response text:', responseText);
      } catch (e) {
        console.error('Could not read response text:', e);
      }
      return false;
    }
  } catch (error) {
    console.error('Error sending webhook:', error);
    return false;
  }
};
