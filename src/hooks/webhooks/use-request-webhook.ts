
import { useState, useEffect } from 'react';
import { useToast } from '@/hooks/use-toast';
import { triggerWebhook } from './webhook-utils';

export const useRequestWebhook = () => {
  const { toast } = useToast();
  const [webhookUrl, setWebhookUrl] = useState('');
  const [webhookMethod, setWebhookMethod] = useState('POST');

  useEffect(() => {
    // Load webhook settings from localStorage on component mount
    setWebhookUrl(localStorage.getItem('webhookUrl') || '');
    setWebhookMethod(localStorage.getItem('webhookMethod') || 'POST');
  }, []);

  const handleWebhookUrlChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const url = e.target.value;
    setWebhookUrl(url);
    localStorage.setItem('webhookUrl', url);
  };
  
  const handleWebhookMethodChange = (value: string) => {
    setWebhookMethod(value);
    localStorage.setItem('webhookMethod', value);
  };
  
  const triggerRequestWebhook = async (requestData: any, indexData: any) => {
    await triggerWebhook(webhookUrl, webhookMethod, requestData, indexData, toast);
  };

  return {
    webhookUrl,
    webhookMethod,
    handleWebhookUrlChange,
    handleWebhookMethodChange,
    triggerRequestWebhook
  };
};
