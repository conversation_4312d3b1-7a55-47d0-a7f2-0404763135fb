
import { useToast } from '@/hooks/use-toast';
import { prepareBasePayload, sendWebhookRequest } from './base-webhook-utils';

/**
 * Triggers a webhook with the provided data
 */
export const triggerWebhook = async (
  url: string,
  method: string,
  requestData: any,
  indexData: any,
  toastFn: ReturnType<typeof useToast>['toast']
) => {
  if (!url) {
    console.log('No webhook URL provided, skipping webhook trigger');
    toastFn({
      title: 'Avertissement',
      description: 'Aucune URL de webhook configurée. Veuillez configurer une URL de webhook dans les paramètres.',
      variant: 'destructive',
    });
    return;
  }
  
  try {
    console.log(`Triggering webhook (${method}):`, url);
    console.log('Request data:', requestData);
    console.log('Index data:', indexData);
    
    // Prepare the data as specified in the requirements
    const payload = {
      ...prepareBasePayload(requestData, indexData),
      // Include full objects for reference if needed
      request: requestData,
      index: indexData
    };
    
    console.log('Preparing to send webhook with payload:', payload);
    
    const success = await sendWebhookRequest(url, method, payload, toastFn);
    
    if (success) {
      console.log('Webhook successfully sent');
      toastFn({
        title: 'Webhook déclenché',
        description: 'Le webhook a été déclenché avec succès.',
      });
    } else {
      console.error('Webhook request failed');
      throw new Error('Failed to trigger webhook');
    }
  } catch (error) {
    console.error('Error triggering webhook:', error);
    toastFn({
      title: 'Avertissement',
      description: 'La demande a été créée, mais nous n\'avons pas pu déclencher le webhook. Vérifiez l\'URL du webhook.',
      variant: 'destructive',
    });
  }
};
