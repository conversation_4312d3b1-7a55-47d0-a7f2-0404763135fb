
/**
 * Sends a webhook with index data to trigger external processes
 * @param indexData The index data to send in the webhook
 * @returns Promise<boolean> indicating success or failure
 */
export async function sendIndexWebhook(indexData: any): Promise<boolean> {
  try {
    const webhookUrl = "https://n8n.paraito.ca/webhook/3251fd48-02e7-4f0c-bb9e-c9215d871a9e";
    
    // Send the index content directly as the webhook body
    const payload = indexData;
    
    console.log('Sending webhook to:', webhookUrl);
    console.log('Webhook payload (index content):', payload);
    
    const response = await fetch(webhookUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(payload)
    });
    
    if (!response.ok) {
      throw new Error(`Webhook failed with status: ${response.status}`);
    }
    
    console.log('Webhook sent successfully');
    return true;
  } catch (error) {
    console.error('Error sending webhook:', error);
    return false;
  }
}
