
import { useToast } from '@/hooks/use-toast';
import { prepareBasePayload, sendWebhookRequest } from './base-webhook-utils';

/**
 * Triggers an acte status webhook with the provided data
 */
export const triggerActeStatusWebhook = async (
  url: string,
  requestData: any,
  indexData: any,
  acteData: any,
  toastFn: ReturnType<typeof useToast>['toast']
) => {
  try {
    console.log(`Triggering acte status webhook:`, url);
    
    if (!url) {
      console.log('No acte webhook URL provided, skipping webhook trigger');
      return;
    }
    
    // Prepare the data for the acte status change webhook
    const payload = {
      event_type: 'acte_status_ready',
      ...prepareBasePayload(requestData, indexData),
      index_name: indexData?.index_name,
      acte_id: acteData?.id,
      acte_number: acteData?.acte_number,
      acte_type: acteData?.acte_type,
      acte_status: acteData?.acte_status,
      // Include timestamps
      created_at: acteData?.created_at,
      updated_at: acteData?.updated_at,
      // Include full objects for reference if needed
      request: requestData,
      index: indexData,
      acte: acteData
    };
    
    const success = await sendWebhookRequest(url, 'POST', payload, toastFn);
    
    if (success) {
      toastFn({
        title: 'Notification envoyée',
        description: 'Le webhook de changement de statut d\'acte a été déclenché avec succès.',
      });
    } else {
      throw new Error('Failed to trigger acte status webhook');
    }
  } catch (error) {
    console.error('Error triggering acte status webhook:', error);
    toastFn({
      title: 'Erreur',
      description: 'Nous n\'avons pas pu déclencher le webhook de changement de statut d\'acte.',
      variant: 'destructive',
    });
  }
};
