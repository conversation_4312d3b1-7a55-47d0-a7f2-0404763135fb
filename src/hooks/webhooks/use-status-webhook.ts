
import { useState, useEffect } from 'react';
import { useToast } from '@/hooks/use-toast';

export const useStatusWebhook = () => {
  const { toast } = useToast();
  const [statusWebhookUrl, setStatusWebhookUrl] = useState('');

  useEffect(() => {
    // Load status webhook URL from localStorage on component mount
    const savedUrl = localStorage.getItem('statusWebhookUrl') || '';
    console.log('Loading statusWebhookUrl from localStorage:', savedUrl);
    setStatusWebhookUrl(savedUrl);
  }, []);

  const handleStatusWebhookUrlChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const url = e.target.value;
    console.log('Setting statusWebhookUrl:', url);
    setStatusWebhookUrl(url);
    localStorage.setItem('statusWebhookUrl', url);
  };

  return {
    statusWebhookUrl,
    handleStatusWebhookUrlChange,
  };
};
