/**
 * Formats a lot number by keeping spaces and dashes, but removing other non-numeric characters
 * @param lotNumber The raw lot number string
 * @returns A properly formatted lot number
 */
export function formatLotNumber(lotNumber: string): string {
  // Only remove non-numeric characters except spaces and dashes
  return lotNumber.replace(/[^0-9\s-]/g, '');
}

/**
 * Default form data structure for the request form
 */
export const DEFAULT_REQUEST_FORM_DATA = {
  seller_name: '',
  seller_address: '',
  numero_lot: '',
  circonscription_fonciere: '',
  paroisse_cadastrale: 'Cadastre du Québec',
  index_initial: true,
  document_state: 'Title Search',
  document_number: '',
  inclure_actes_radies: true,
  sales_years: 10,
  hypotheques_years: 30,
};

/**
 * Type definition for the request form data
 */
export type RequestFormData = typeof DEFAULT_REQUEST_FORM_DATA;

/**
 * Validates lot number format
 * @param lotNumber The lot number to validate
 * @returns Boolean indicating if the lot number is valid
 */
export function isValidLotNumber(lotNumber: string): boolean {
  // Basic validation - can be expanded based on specific requirements
  return lotNumber.trim().length > 0 && lotNumber.trim().length <= 7;
}

/**
 * Validates the entire form data
 * @param formData The form data to validate
 * @returns An object containing validation status and any error messages
 */
export function validateFormData(formData: RequestFormData): { isValid: boolean; errors: Record<string, string> } {
  const errors: Record<string, string> = {};
  
  if (!formData.seller_name.trim()) {
    errors.seller_name = 'Le nom du vendeur est requis';
  }
  
  if (!formData.seller_address.trim()) {
    errors.seller_address = 'L\'adresse du vendeur est requise';
  }
  
  if (!isValidLotNumber(formData.numero_lot)) {
    errors.numero_lot = 'Le numéro de lot est requis et doit être valide';
  }
  
  if (!formData.circonscription_fonciere.trim()) {
    errors.circonscription_fonciere = 'La circonscription foncière est requise';
  }
  
  return {
    isValid: Object.keys(errors).length === 0,
    errors
  };
}
