
import { useState, useEffect } from 'react';

/**
 * Hook for persisting form data in localStorage
 * @param key The localStorage key to use
 * @param initialData Default data to use if nothing is in localStorage
 * @returns [formData, setFormData] - State and setter for form data
 */
export function useFormPersistence<T>(key: string, initialData: T): [T, React.Dispatch<React.SetStateAction<T>>] {
  // Initialize form data from localStorage or with default values
  const [data, setData] = useState<T>(() => {
    const savedData = localStorage.getItem(key);
    if (savedData) {
      try {
        return JSON.parse(savedData);
      } catch (error) {
        console.error(`Error parsing saved data for ${key}:`, error);
      }
    }
    
    // Default values if no saved data exists
    return initialData;
  });
  
  // Save form data to localStorage whenever it changes
  useEffect(() => {
    localStorage.setItem(key, JSON.stringify(data));
  }, [data, key]);

  return [data, setData];
}

/**
 * Clear persisted form data from localStorage
 * @param key The localStorage key to clear
 */
export function clearPersistedForm(key: string): void {
  localStorage.removeItem(key);
}
