
import { useRequestWebhook } from './webhooks/use-request-webhook';
import { useStatusWebhook } from './webhooks/use-status-webhook';
import { useActeWebhook } from './webhooks/use-acte-webhook';
import { triggerWebhook as triggerWebhookUtil, triggerStatusWebhook, triggerActeStatusWebhook } from './webhooks/webhook-utils';
import { useToast } from './use-toast';

export const useWebhook = () => {
  const { toast } = useToast();
  
  // Request webhook for manual triggering when new requests are created
  const {
    webhookUrl,
    webhookMethod,
    handleWebhookUrlChange,
    handleWebhookMethodChange,
    triggerRequestWebhook
  } = useRequestWebhook();
  
  // Status webhook for automatic triggering when index is ready for analysis
  const { 
    statusWebhookUrl, 
    handleStatusWebhookUrlChange 
  } = useStatusWebhook();
  
  // Acte webhook for automatic triggering when acte is ready for analysis
  const { 
    acteWebhookUrl, 
    handleActeWebhookUrlChange 
  } = useActeWebhook();

  // This provides backwards compatibility with existing code
  const triggerWebhookWrapper = async (requestData: any, indexData: any) => {
    console.log('Triggering webhook wrapper with URL:', webhookUrl);
    console.log('Webhook method:', webhookMethod);
    // Ensure we're using the current webhookUrl value, not relying on closure variables
    await triggerWebhookUtil(webhookUrl, webhookMethod, requestData, indexData, toast);
  };

  // This provides backwards compatibility with existing code
  const triggerStatusWebhookWrapper = async (requestData: any, indexData: any) => {
    await triggerStatusWebhook(statusWebhookUrl, requestData, indexData, toast);
  };
  
  // Add a function to trigger the acte webhook
  const triggerActeStatusWebhookWrapper = async (requestData: any, indexData: any, acteData: any) => {
    await triggerActeStatusWebhook(acteWebhookUrl, requestData, indexData, acteData, toast);
  };

  return {
    webhookUrl,
    webhookMethod,
    handleWebhookUrlChange,
    handleWebhookMethodChange,
    statusWebhookUrl,
    handleStatusWebhookUrlChange,
    acteWebhookUrl,
    handleActeWebhookUrlChange,
    triggerWebhook: triggerWebhookWrapper,
    triggerStatusWebhook: triggerStatusWebhookWrapper,
    triggerActeStatusWebhook: triggerActeStatusWebhookWrapper
  };
};
