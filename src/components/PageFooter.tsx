
import React from 'react';
import { Link } from 'react-router-dom';
import { useTheme } from '@/components/ThemeProvider';

const PageFooter: React.FC = () => {
  const { theme } = useTheme();

  return (
    <footer className="border-t border-border/60 bg-secondary/30 py-6">
      <div className="container flex flex-col sm:flex-row justify-between items-center px-4 sm:px-6 lg:px-8">
        <div className="flex flex-col items-center sm:items-start mb-4 sm:mb-0">
          <Link to="/" className="flex items-center gap-2 mb-2">
            {theme === 'dark' ? (
              <img 
                src="/lovable-uploads/1e04b4a5-6cbc-412d-90a8-c4be381f924a.png" 
                alt="Notaflow Logo Dark" 
                className="h-8 w-auto"
              />
            ) : (
              <img 
                src="/lovable-uploads/73d63e74-6e16-44ec-9e12-e2ac0ac70ed3.png" 
                alt="Notaflow Logo Light" 
                className="h-8 w-auto"
              />
            )}
            <span className="font-semibold font-playfair">Notaflow</span>
          </Link>
          <p className="text-xs text-muted-foreground">© 2025 ParAito. Tous droits réservés.</p>
        </div>
        <div className="flex items-center space-x-6">
          <Link to="#" className="text-sm text-muted-foreground hover:text-primary transition-colors">
            Conditions d'utilisation
          </Link>
          <Link to="#" className="text-sm text-muted-foreground hover:text-primary transition-colors">
            Politique de confidentialité
          </Link>
        </div>
      </div>
    </footer>
  );
};

export default PageFooter;
