
import React from 'react';
import { cn } from '@/lib/utils';

/**
 * @deprecated This component is no longer in use as we've removed all status indicators from the UI.
 * It's kept here for reference but should not be used.
 */

type StatusType = 'pending' | 'approved' | 'rejected' | 'inprogress';

interface StatusBadgeProps {
  status: StatusType;
  className?: string;
}

const StatusBadge: React.FC<StatusBadgeProps> = ({ status, className }) => {
  console.warn('StatusBadge component is deprecated and should not be used');
  return null;
};

export default StatusBadge;
