
import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { formatDistanceToNow } from 'date-fns';
import { fr } from 'date-fns/locale';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Button } from '@/components/ui/button';
import { Bell, CheckCheck, Loader2 } from 'lucide-react';
import { useNotifications } from '@/hooks/use-notifications';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import { cn } from '@/lib/utils';

const NotificationsMenu: React.FC = () => {
  const { 
    notifications, 
    unreadCount, 
    isLoading, 
    markAsRead, 
    markAllAsRead 
  } = useNotifications();

  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button variant="ghost" size="icon" className="relative rounded-full w-9 h-9 flex items-center justify-center">
          <Bell className="h-5 w-5" />
          {unreadCount > 0 && (
            <span className="absolute -top-0.5 -right-0.5 h-4 w-4 rounded-full bg-destructive text-[10px] font-medium flex items-center justify-center text-destructive-foreground">
              {unreadCount > 99 ? '99+' : unreadCount}
            </span>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-80 p-0" align="end">
        <div className="flex justify-between items-center p-4">
          <h4 className="font-medium text-sm">Notifications</h4>
          {notifications.length > 0 && (
            <Button 
              variant="ghost" 
              size="sm" 
              className="h-auto p-0 text-xs text-muted-foreground"
              onClick={markAllAsRead}
            >
              <CheckCheck className="h-3.5 w-3.5 mr-1" />
              Tout marquer comme lu
            </Button>
          )}
        </div>
        
        <Separator />
        
        <ScrollArea className="max-h-[300px]">
          {isLoading ? (
            <div className="flex justify-center items-center p-6">
              <Loader2 className="h-6 w-6 animate-spin text-muted-foreground" />
            </div>
          ) : notifications.length === 0 ? (
            <div className="flex flex-col items-center justify-center py-8 px-4 text-center">
              <Bell className="h-10 w-10 text-muted-foreground/50 mb-2" />
              <p className="text-sm text-muted-foreground mb-1">Pas de notifications</p>
              <p className="text-xs text-muted-foreground/70">
                Vous recevrez des notifications lorsque vos demandes seront traitées
              </p>
            </div>
          ) : (
            <div>
              {notifications.map((notification) => (
                <div 
                  key={notification.id}
                  onClick={() => markAsRead(notification.id)}
                  className={cn(
                    "p-4 hover:bg-muted/50 cursor-pointer border-b border-border/50 last:border-0",
                    !notification.is_read && "bg-muted/30"
                  )}
                >
                  {notification.request_id ? (
                    <Link 
                      to={`/requests/${notification.request_id}`}
                      className="block"
                    >
                      <NotificationContent notification={notification} />
                    </Link>
                  ) : (
                    <NotificationContent notification={notification} />
                  )}
                </div>
              ))}
            </div>
          )}
        </ScrollArea>
      </PopoverContent>
    </Popover>
  );
};

const NotificationContent = ({ notification }: { notification: { title: string; content: string; created_at: string; is_read: boolean } }) => (
  <>
    <div className="flex justify-between items-start mb-1">
      <h5 className={cn(
        "text-sm font-medium leading-none",
        !notification.is_read && "text-primary"
      )}>
        {notification.title}
      </h5>
      <span className="text-[10px] text-muted-foreground whitespace-nowrap ml-2">
        {formatDistanceToNow(new Date(notification.created_at), { 
          addSuffix: true,
          locale: fr
        })}
      </span>
    </div>
    <p className="text-xs text-muted-foreground">{notification.content}</p>
  </>
);

export default NotificationsMenu;
