
import React from 'react';
import { Calendar, User, FileText, MapPin, Clock } from 'lucide-react';
import { NotaryRequest } from '@/lib/data';
import { cn } from '@/lib/utils';
import { formatDistanceToNow } from 'date-fns';
import { fr } from 'date-fns/locale';
import { Link } from 'react-router-dom';
import { Badge } from '@/components/ui/badge';

interface RequestCardProps {
  request: NotaryRequest;
  index: number;
}

const RequestCard: React.FC<RequestCardProps> = ({ request, index }) => {
  const formattedDate = formatDistanceToNow(new Date(request.created_at), { 
    addSuffix: true,
    locale: fr 
  });

  // Status translation function - Updated with exact mapping
  const translateStatus = (status: string): string => {
    switch (status?.toLowerCase()) {
      case 'pending':
        return 'En attente';
      case 'inprogress':
      case 'in progress':
        return 'En cours';
      case 'processing_phase_1':
      case 'phase 1':
        return 'Phase 1';
      case 'processing_phase_2':
      case 'phase 2':
        return 'Phase 2';
      case 'processing_phase_3':
      case 'phase 3':
        return 'Phase 3';
      case 'completed':
      case 'approved': // Added to correctly map 'approved' to 'Complété'
        return 'Complété';
      case 'finalizing':
        return 'Finalisation';
      case 'error':
        return 'Erreur';
      default:
        return status || 'Inconnu';
    }
  };

  // Status badge variant based on status
  const getStatusVariant = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'completed':
      case 'approved': // Added to correctly map 'approved' to 'completed' badge style
        return 'completed';
      case 'error':
        return 'error';
      case 'finalizing':
        return 'finalizing';
      case 'pending':
        return 'pending';
      case 'inprogress':
      case 'in progress':
        return 'in-progress';
      case 'processing_phase_1':
      case 'phase 1':
        return 'phase1';
      case 'processing_phase_2':
      case 'phase 2':
        return 'phase2';
      case 'processing_phase_3':
      case 'phase 3':
        return 'phase3';
      default:
        return 'secondary';
    }
  };

  return (
    <Link to={`/requests/${request.id}`}>
      <div 
        className={cn(
          "relative bg-card rounded-xl p-4 shadow-sm border border-border/40",
          "transition-all duration-300 ease-out animate-fade-in-up",
          "hover:shadow-md hover:border-primary/30 hover:-translate-y-1"
        )}
        style={{ 
          animationDelay: `${index * 50}ms`,
          animationFillMode: 'both'
        }}
      >
        <div className="flex flex-col space-y-3">
          <div className="flex justify-between items-start">
            <h3 className="font-medium text-foreground truncate">{request.seller_name}</h3>
            {request.status && (
              <Badge variant={getStatusVariant(request.status)} className="ml-2 text-xs">
                {translateStatus(request.status)}
              </Badge>
            )}
          </div>
          
          <div className="space-y-2">
            {request.property_address && (
              <div className="flex items-start space-x-2">
                <MapPin className="w-4 h-4 text-muted-foreground mt-0.5 flex-shrink-0" />
                <p className="text-sm text-muted-foreground truncate">
                  {request.property_address}
                </p>
              </div>
            )}
            
            <div className="flex items-center space-x-2">
              <Calendar className="w-4 h-4 text-muted-foreground" />
              <p className="text-xs text-muted-foreground">{formattedDate}</p>
            </div>
            
            {request.request_type && (
              <div className="flex items-center space-x-2">
                <FileText className="w-4 h-4 text-muted-foreground" />
                <p className="text-xs text-muted-foreground">{request.request_type}</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </Link>
  );
};

export default RequestCard;
