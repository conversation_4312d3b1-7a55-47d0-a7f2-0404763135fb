import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { cn } from '@/lib/utils';
import { ThemeToggle } from './ThemeToggle';
import UserMenu from './UserMenu';
import { useAuth } from '@/context/AuthContext';
import { LayoutDashboard } from 'lucide-react';
import NotificationsMenu from './NotificationsMenu';
import { useTheme } from '@/components/ThemeProvider';
const TopNavbar = () => {
  const location = useLocation();
  const {
    user
  } = useAuth();
  const {
    theme
  } = useTheme();
  return <header className="sticky top-0 z-50 w-full border-b border-border/40 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container flex h-16 items-center">
        {/* Logo and Brand */}
        <div className="flex items-center">
          <Link to="/" className="flex items-center gap-2 mr-8">
            {theme === 'dark' ? <img src="/lovable-uploads/1e04b4a5-6cbc-412d-90a8-c4be381f924a.png" alt="Logo" className="h-10 w-auto" /> : <img src="/lovable-uploads/73d63e74-6e16-44ec-9e12-e2ac0ac70ed3.png" alt="Logo" className="h-10 w-auto" />}
            <div className="flex items-center">
              {/* Notaflow text image with par ParAito to the right */}
              <div className="flex items-center">
                {theme === 'dark' ? <img src="/lovable-uploads/48843514-d9fe-484e-934f-34a6e6d2c523.png" alt="Notaflow" className="h-12 w-auto object-contain" /> : <img src="/lovable-uploads/7cb008a9-3366-429d-b8b4-0d6a6a57c636.png" alt="Notaflow" className="h-12 w-auto object-contain" />}
                <span className="ml-2 italic text-foreground tracking-wide text-xs font">par ParAito.</span>
              </div>
            </div>
          </Link>
          
          {/* Main Navigation */}
          <nav className="hidden md:flex items-center space-x-1">
            <Link to="/" className={cn("flex items-center px-3 py-2 rounded-md text-sm font-medium transition-colors", location.pathname === "/" ? "bg-secondary text-foreground" : "text-muted-foreground hover:bg-secondary/80 hover:text-foreground")}>
              <LayoutDashboard className="h-4 w-4 mr-2" />
              Tableau de bord
            </Link>
          </nav>
        </div>

        {/* Spacer to push content to the sides */}
        <div className="flex-1"></div>

        {/* Right side controls */}
        <div className="flex items-center gap-3">
          <NotificationsMenu />
          <ThemeToggle />
          {user && <UserMenu />}
        </div>
      </div>
    </header>;
};
export default TopNavbar;