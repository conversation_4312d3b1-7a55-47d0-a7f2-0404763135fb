
import React from "react";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { User, MapPin } from "lucide-react";

interface RequestFormSellerProps {
  formData: {
    seller_name: string;
    seller_address?: string;
  };
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
}

const RequestFormSeller = ({ formData, onChange }: RequestFormSellerProps) => {
  return (
    <div className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="seller_name" className="flex items-center gap-2">
          <User className="h-4 w-4 text-muted-foreground" />
          Nom du vendeur
        </Label>
        <Input
          id="seller_name"
          name="seller_name"
          placeholder="Entrez le nom du vendeur"
          value={formData.seller_name}
          onChange={onChange}
          required
          className="transition-all focus-visible:border-primary"
        />
        <p className="text-xs text-muted-foreground pl-1">
          Le nom complet du vendeur tel qu'il apparaît sur les documents officiels
        </p>
      </div>
      
      <div className="space-y-2">
        <Label htmlFor="seller_address" className="flex items-center gap-2">
          <MapPin className="h-4 w-4 text-muted-foreground" />
          Adresse du vendeur
        </Label>
        <Input
          id="seller_address"
          name="seller_address"
          placeholder="Entrez l'adresse du vendeur"
          value={formData.seller_address || ''}
          onChange={onChange}
          className="transition-all focus-visible:border-primary"
        />
        <p className="text-xs text-muted-foreground pl-1">
          L'adresse complète du vendeur
        </p>
      </div>
    </div>
  );
};

export default RequestFormSeller;
