
import React from "react";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { MapPin, Building } from "lucide-react";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface RequestFormPropertyProps {
  formData: {
    numero_lot: string;
    circonscription_fonciere: string;
  };
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onSelectChange: (name: string, value: string) => void;
}

const CIRCONSCRIPTION_OPTIONS = [
  "Abitibi", "Argenteuil", "Arthabaska", "<PERSON><PERSON>", "<PERSON>har<PERSON><PERSON>",
  "<PERSON>chasse", "Berthier", "Bonaventure 1", "Bonaventure 2",
  "Brome", "Chambly", "Champlain", "Charlevoix 1", "Charlevoix 2",
  "Châteauguay", "Chicoutimi", "Coatic<PERSON>", "Compton", "<PERSON>ux-Montagnes",
  "Do<PERSON>ester", "<PERSON>", "Frontenac", "<PERSON><PERSON><PERSON>", "Gati<PERSON><PERSON>",
  "Hull", "Huntingdon", "Îles-de-la-Madeleine", "<PERSON><PERSON><PERSON>", "Kamouraska",
  "Labelle", "Lac-Saint-Jean-Est", "Lac-Saint-Jean-Ouest", "Laprairie",
  "L'Assomption", "La Tuque", "Laval", "Lévis", "L'Islet", "Lotbinière",
  "Maskinongé", "Matane", "Matapédia", "Missisquoi", "Montcalm",
  "Montmagny", "Montmorency", "Montréal", "Nicolet (Nicolet 2)",
  "Papineau", "Pontiac", "Portneuf", "Québec", "Rimouski",
  "Richmond", "Richelieu", "Rouville", "Rouyn-Noranda", "Saguenay",
  "Sainte-Anne-des-Monts", "Saint-Hyacinthe", "Saint-Jean", "Sept-Îles",
  "Shawinigan", "Shefford", "Sherbooke", "Stanstead", "Témiscamingue",
  "Témiscouata", "Terrebonne", "Thetford", "Trois-Rivières", "Vaudreuil", "Verchères"
];

const RequestFormProperty = ({ formData, onChange, onSelectChange }: RequestFormPropertyProps) => {
  return (
    <div className="grid grid-cols-2 gap-4">
      <div className="space-y-2">
        <Label htmlFor="numero_lot" className="flex items-center gap-2">
          <MapPin className="h-4 w-4 text-muted-foreground" />
          Numéro de lot
        </Label>
        <Input
          id="numero_lot"
          name="numero_lot"
          placeholder="Entrez le numéro de lot"
          value={formData.numero_lot}
          onChange={onChange}
          required
          className="transition-all focus-visible:border-primary"
        />
        <p className="text-xs text-muted-foreground pl-1">
          Numéro de lot tel qu'inscrit au Registre foncier (max 7 caractères, chiffres et tirets uniquement)
        </p>
      </div>
      
      <div className="space-y-2">
        <Label htmlFor="circonscription_fonciere" className="flex items-center gap-2">
          <Building className="h-4 w-4 text-muted-foreground" />
          Circonscription foncière
        </Label>
        <Select
          value={formData.circonscription_fonciere}
          onValueChange={(value) => onSelectChange("circonscription_fonciere", value)}
        >
          <SelectTrigger id="circonscription_fonciere" className="w-full transition-all focus-visible:border-primary">
            <SelectValue placeholder="Sélectionnez une circonscription foncière" />
          </SelectTrigger>
          <SelectContent>
            {CIRCONSCRIPTION_OPTIONS.map((option) => (
              <SelectItem key={option} value={option}>
                {option}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        <p className="text-xs text-muted-foreground pl-1">
          Sélectionnez la circonscription foncière correspondante
        </p>
      </div>
    </div>
  );
};

export default RequestFormProperty;
