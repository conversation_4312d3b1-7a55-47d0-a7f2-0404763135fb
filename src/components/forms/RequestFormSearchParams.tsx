
import React from 'react';
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Calendar<PERSON>lock, AlertCircle, FileCheck2 } from 'lucide-react';

interface RequestFormSearchParamsProps {
  formData: {
    inclure_actes_radies: boolean;
    sales_years: number;
    hypotheques_years: number;
  };
  onChange: (name: string, value: boolean | number) => void;
}

const RequestFormSearchParams = ({ formData, onChange }: RequestFormSearchParamsProps) => {
  // Generate an array of years from 1 to 50
  const years = Array.from({ length: 50 }, (_, i) => i + 1);
  
  // Ensure we have valid values for select dropdowns
  const salesYearsValue = formData?.sales_years?.toString() || "10";
  const hypothequesYearsValue = formData?.hypotheques_years?.toString() || "30";

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between space-x-2">
        <div className="flex flex-col space-y-1">
          <Label htmlFor="inclure_actes_radies" className="flex items-center gap-2">
            <FileCheck2 className="h-4 w-4 text-muted-foreground" />
            Inclure les actes radiés?
          </Label>
          <span className="text-xs text-muted-foreground">
            Inclure les actes qui ont été radiés dans les résultats
          </span>
        </div>
        <Switch
          id="inclure_actes_radies"
          checked={formData?.inclure_actes_radies ?? true}
          onCheckedChange={(checked) => onChange('inclure_actes_radies', checked)}
        />
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="sales_years" className="flex items-center gap-2">
            <CalendarClock className="h-4 w-4 text-muted-foreground" />
            Ventes (années)
          </Label>
          <Select
            value={salesYearsValue}
            onValueChange={(value) => onChange('sales_years', parseInt(value))}
          >
            <SelectTrigger id="sales_years">
              <SelectValue placeholder="Sélectionner" />
            </SelectTrigger>
            <SelectContent>
              {years.map((year) => (
                <SelectItem key={year} value={year.toString()}>
                  {year} {year === 1 ? 'an' : 'ans'}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <p className="text-xs text-muted-foreground">
            Identifier les ventes des dernières années
          </p>
        </div>

        <div className="space-y-2">
          <Label htmlFor="hypotheques_years" className="flex items-center gap-2">
            <AlertCircle className="h-4 w-4 text-muted-foreground" />
            Hypothèques (années)
          </Label>
          <Select
            value={hypothequesYearsValue}
            onValueChange={(value) => onChange('hypotheques_years', parseInt(value))}
          >
            <SelectTrigger id="hypotheques_years">
              <SelectValue placeholder="Sélectionner" />
            </SelectTrigger>
            <SelectContent>
              {years.map((year) => (
                <SelectItem key={year} value={year.toString()}>
                  {year} {year === 1 ? 'an' : 'ans'}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <p className="text-xs text-muted-foreground">
            Identifier les hypothèques des dernières années
          </p>
        </div>
      </div>
    </div>
  );
};

export default RequestFormSearchParams;
