
import React from "react";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select";

interface RequestFormDocumentProps {
  documentState: string;
  documentNumber: string;
  onInputChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onSelectChange: (name: string, value: string) => void;
}

const RequestFormDocument = ({ 
  documentState, 
  documentNumber, 
  onInputChange, 
  onSelectChange 
}: RequestFormDocumentProps) => {
  return (
    <>
      <div className="space-y-2">
        <Label htmlFor="document_state">Type de demande</Label>
        <Select 
          value={documentState}
          onValueChange={(value) => onSelectChange('document_state', value)}
        >
          <SelectTrigger>
            <SelectValue placeholder="Sélectionnez un type" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="Property Transfer">Transfert de propriété</SelectItem>
            <SelectItem value="Mortgage Refinancing">Refinancement hypothécaire</SelectItem>
            <SelectItem value="Title Search">Recherche de titre</SelectItem>
            <SelectItem value="Will Preparation">Préparation de testament</SelectItem>
          </SelectContent>
        </Select>
      </div>
      
      <div className="space-y-2">
        <Label htmlFor="document_number">Numéro de document</Label>
        <Input
          id="document_number"
          name="document_number"
          placeholder="Entrez le numéro de document"
          value={documentNumber}
          onChange={onInputChange}
        />
      </div>
    </>
  );
};

export default RequestFormDocument;
