
import React from "react";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select";

interface RequestFormWebhookProps {
  webhookUrl: string;
  webhookMethod: string;
  onUrlChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onMethodChange: (value: string) => void;
}

const RequestFormWebhook = ({ 
  webhookUrl, 
  webhookMethod, 
  onUrlChange, 
  onMethodChange 
}: RequestFormWebhookProps) => {
  return (
    <div className="space-y-4">
      <Label htmlFor="webhook_url" className="flex items-center text-sm font-medium">
        Configuration du Webhook (pour l'automatisation n8n)
      </Label>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="md:col-span-2">
          <Input
            id="webhook_url"
            name="webhook_url"
            placeholder="Entrez l'URL du webhook n8n (ex: https://marcoparaito.app.n8n.cloud/webhook/...)"
            value={webhookUrl}
            onChange={onUrlChange}
            className="w-full"
          />
        </div>
        
        <div>
          <Select 
            value={webhookMethod}
            onValueChange={onMethodChange}
          >
            <SelectTrigger>
              <SelectValue placeholder="Méthode" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="GET">GET</SelectItem>
              <SelectItem value="POST">POST</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
      
      <div className="text-xs text-muted-foreground space-y-1">
        <p>L'URL et la méthode seront sauvegardées localement pour une utilisation ultérieure.</p>
        <p className="font-medium">URL actuelle: {webhookUrl || "Aucune URL configurée"}</p>
      </div>
    </div>
  );
};

export default RequestFormWebhook;
