import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select } from "@/components/ui/select";
import { Trash, Plus } from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

interface SampleActe {
  description: string;
  type: string;
  date: string;
}

interface RequestFormActesProps {
  actes: SampleActe[];
  onActeChange: (index: number, field: string, value: string) => void;
  onAddActe: () => void;
  onRemoveActe: (index: number) => void;
}

const RequestFormActes: React.FC<RequestFormActesProps> = ({
  actes,
  onActeChange,
  onAddActe,
  onRemoveActe
}) => {
  const getTypeVariant = (type: string) => {
    switch (type) {
      case 'Emphytéose':
        return 'inscription-emphyteose';
      case 'Bail':
        return 'inscription-bail';
      case 'Cession':
        return 'inscription-cession';
      case 'Radiation':
        return 'inscription-radiation';
      default:
        return 'secondary';
    }
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="text-lg font-medium">Inscriptions (Actes)</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <p className="text-sm text-muted-foreground mb-4">
          Ajoutez les inscriptions associées à cette demande. Ces données seront transmises au workflow n8n pour traitement.
          L'indice principal sera numéroté 1.0, et les inscriptions suivantes seront 1.1, 1.2, etc.
        </p>
        
        {actes.map((acte, index) => (
          <div key={index} className="grid grid-cols-1 md:grid-cols-12 gap-4 p-4 border rounded-md relative">
            <div className="md:col-span-5 space-y-2">
              <Label htmlFor={`acte-description-${index}`}>Description</Label>
              <Input
                id={`acte-description-${index}`}
                value={acte.description}
                onChange={(e) => onActeChange(index, 'description', e.target.value)}
                placeholder="Description de l'inscription"
              />
            </div>
            
            <div className="md:col-span-3 space-y-2">
              <Label htmlFor={`acte-type-${index}`}>Type</Label>
              <Select
                value={acte.type}
                onValueChange={(value) => onActeChange(index, 'type', value)}
              >
                <option value="Emphytéose">Emphytéose</option>
                <option value="Bail">Bail</option>
                <option value="Cession">Cession</option>
                <option value="Radiation">Radiation</option>
                <option value="Autre">Autre</option>
              </Select>
              <Badge variant={getTypeVariant(acte.type)} className="mt-1">
                {acte.type}
              </Badge>
            </div>
            
            <div className="md:col-span-3 space-y-2">
              <Label htmlFor={`acte-date-${index}`}>Date</Label>
              <Input
                id={`acte-date-${index}`}
                type="date"
                value={acte.date.split('T')[0]}
                onChange={(e) => {
                  const newDate = new Date(e.target.value);
                  onActeChange(index, 'date', newDate.toISOString());
                }}
              />
            </div>
            
            <div className="md:col-span-1 flex items-end justify-end">
              <Button
                type="button"
                variant="ghost"
                size="icon"
                onClick={() => onRemoveActe(index)}
                className="text-destructive hover:text-destructive hover:bg-destructive/10"
              >
                <Trash className="h-4 w-4" />
              </Button>
            </div>
            
            <div className="absolute -left-3 top-1/2 -translate-y-1/2 bg-muted text-muted-foreground px-1.5 py-0.5 text-xs rounded-full">
              1.{index + 1}
            </div>
          </div>
        ))}
        
        <Button
          type="button"
          variant="outline"
          onClick={onAddActe}
          className="w-full"
        >
          <Plus className="h-4 w-4 mr-2" />
          Ajouter une inscription
        </Button>
      </CardContent>
    </Card>
  );
};

export default RequestFormActes;
