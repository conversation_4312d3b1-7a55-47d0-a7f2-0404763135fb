import React from 'react';
import { useNavigate } from 'react-router-dom';
import { useRequestForm } from '@/hooks/use-request-form';
import { Card, CardContent, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ArrowLeft, Save, User, FileText, Info, Search } from 'lucide-react';
import { Separator } from '@/components/ui/separator';
import RequestFormProperty from './forms/RequestFormProperty';
import RequestFormSeller from './forms/RequestFormSeller';
import RequestFormSearchParams from './forms/RequestFormSearchParams';

const NewRequestForm: React.FC = () => {
  const navigate = useNavigate();
  const { 
    formData,
    isLoading,
    handleChange,
    handleSelectChange,
    handleBooleanOrNumberChange,
    handleSubmit
  } = useRequestForm();
  
  return (
    <form onSubmit={handleSubmit} className="animate-fade-in-up">
      <Card className="shadow-sm">
        <CardContent className="space-y-6 p-6">
          <div className="space-y-4">
            <h2 className="text-lg font-medium flex items-center gap-2">
              <User className="h-5 w-5 text-primary" />
              Informations du vendeur
            </h2>
            
            <RequestFormSeller formData={formData} onChange={handleChange} />
          </div>
          
          <Separator />
          
          <div className="space-y-4">
            <h2 className="text-lg font-medium flex items-center gap-2">
              <FileText className="h-5 w-5 text-primary" />
              Informations sur la propriété
            </h2>
            
            <RequestFormProperty 
              formData={formData} 
              onChange={handleChange} 
              onSelectChange={handleSelectChange}
            />
            
            <div className="bg-muted/50 p-3 rounded-md flex items-start gap-2">
              <Info className="h-5 w-5 text-muted-foreground shrink-0 mt-0.5" />
              <div>
                <p className="text-sm font-medium">Paroisse cadastrale: Cadastre du Québec</p>
                <p className="text-xs text-muted-foreground">
                  Cette valeur est sélectionnée automatiquement. D'autres documents pourraient avoir une paroisse cadastrale différente si nécessaire.
                </p>
              </div>
            </div>
          </div>
          
          <Separator />
          
          <div className="space-y-4">
            <h2 className="text-lg font-medium flex items-center gap-2">
              <Search className="h-5 w-5 text-primary" />
              Paramètres de recherche
            </h2>
            
            <RequestFormSearchParams 
              formData={formData}
              onChange={handleBooleanOrNumberChange}
            />
          </div>
        </CardContent>
        
        <CardFooter className="flex justify-between border-t p-6">
          <Button
            type="button"
            variant="outline"
            onClick={() => navigate('/')}
            className="flex items-center"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Retour
          </Button>
          <Button type="submit" disabled={isLoading}>
            {isLoading ? (
              <span className="flex items-center">
                <span className="animate-spin mr-2 h-4 w-4 border-t-2 border-b-2 border-current rounded-full" />
                Traitement...
              </span>
            ) : (
              <span className="flex items-center">
                <Save className="h-4 w-4 mr-2" />
                Soumettre
              </span>
            )}
          </Button>
        </CardFooter>
      </Card>
    </form>
  );
};

export default NewRequestForm;
