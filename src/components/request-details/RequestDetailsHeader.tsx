
import React, { useState } from 'react';
import { format } from 'date-fns';
import { fr } from 'date-fns/locale';
import { User, MapPin, CalendarDays, FileText, ChevronDown, Link, Folder, CheckSquare, Calendar, AlertCircle } from 'lucide-react';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';

interface RequestDetailsHeaderProps {
  request: any;
}

const RequestDetailsHeader: React.FC<RequestDetailsHeaderProps> = ({ request }) => {
  const [isOpen, setIsOpen] = useState(false);

  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), 'PPP', { locale: fr });
    } catch (error) {
      return 'Date invalide';
    }
  };

  return (
    <div className="space-y-6">
      <div className="grid md:grid-cols-2 gap-6">
        <div className="space-y-4">
          <div className="flex items-start space-x-3">
            <User className="h-5 w-5 text-primary/70" />
            <div>
              <p className="text-sm font-medium text-muted-foreground">Vendeur</p>
              <p className="text-base font-medium">{request.seller_name || 'Non spécifié'}</p>
            </div>
          </div>
          
          <div className="flex items-start space-x-3">
            <MapPin className="h-5 w-5 text-primary/70" />
            <div>
              <p className="text-sm font-medium text-muted-foreground">Propriété</p>
              <p className="text-base font-medium">{request.seller_address || 'Non spécifié'}</p>
            </div>
          </div>

          <div className="flex items-start space-x-3">
            <CheckSquare className="h-5 w-5 text-primary/70" />
            <div>
              <p className="text-sm font-medium text-muted-foreground">Inclure les actes radiés</p>
              <p className="text-base font-medium">{request.inclure_actes_radies ? 'Oui' : 'Non'}</p>
            </div>
          </div>
        </div>
        
        <div className="space-y-4">
          <div className="flex items-start space-x-3">
            <CalendarDays className="h-5 w-5 text-primary/70" />
            <div>
              <p className="text-sm font-medium text-muted-foreground">Dernière mise à jour</p>
              <p className="text-base font-medium">{formatDate(request.updated_at)}</p>
            </div>
          </div>
          
          <div className="flex items-start space-x-3">
            <Calendar className="h-5 w-5 text-primary/70" />
            <div>
              <p className="text-sm font-medium text-muted-foreground">Ventes</p>
              <p className="text-base font-medium">
                {request.sales_years || 10} {parseInt(request.sales_years) === 1 ? 'an' : 'ans'}
              </p>
            </div>
          </div>
            
          <div className="flex items-start space-x-3">
            <AlertCircle className="h-5 w-5 text-primary/70" />
            <div>
              <p className="text-sm font-medium text-muted-foreground">Hypothèques</p>
              <p className="text-base font-medium">
                {request.hypotheques_years || 30} {parseInt(request.hypotheques_years) === 1 ? 'an' : 'ans'}
              </p>
            </div>
          </div>

          {/* Document final */}
          {request.final_doc_link && (
            <div className="flex items-start space-x-3">
              <FileText className="h-5 w-5 text-primary/70" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">Document final</p>
                <a 
                  href={request.final_doc_link} 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="text-base font-medium text-primary hover:underline flex items-center"
                >
                  <span>Voir le document</span>
                  <Link className="h-4 w-4 ml-1" />
                </a>
              </div>
            </div>
          )}
          
          {/* Dossier */}
          {request.folder_link && (
            <div className="flex items-start space-x-3">
              <Folder className="h-5 w-5 text-primary/70" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">Dossier</p>
                <a 
                  href={request.folder_link} 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="text-base font-medium text-primary hover:underline flex items-center"
                >
                  <span>Accéder au dossier</span>
                  <Link className="h-4 w-4 ml-1" />
                </a>
              </div>
            </div>
          )}
        </div>
      </div>
      
      {/* Collapsible Complete Summary section */}
      {request.complete_summary && request.complete_summary.length > 0 && (
        <div className="border-t border-border/50 pt-4 mt-2">
          <Collapsible open={isOpen} onOpenChange={setIsOpen} className="w-full">
            <div className="flex items-center space-x-3 mb-2">
              <FileText className="h-5 w-5 text-primary/70" />
              <CollapsibleTrigger className="flex items-center space-x-2 text-sm font-medium hover:text-primary transition-colors">
                <span>Résumé complet</span>
                <ChevronDown className={`h-4 w-4 transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`} />
              </CollapsibleTrigger>
            </div>
            <CollapsibleContent className="pl-8 space-y-2 data-[state=open]:animate-slideDown data-[state=closed]:animate-slideUp">
              {request.complete_summary.map((summary: string, idx: number) => (
                <p key={idx} className="text-sm text-muted-foreground bg-muted/30 p-3 rounded-md">
                  {summary}
                </p>
              ))}
            </CollapsibleContent>
          </Collapsible>
        </div>
      )}
    </div>
  );
};

export default RequestDetailsHeader;
