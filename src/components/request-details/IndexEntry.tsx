
import React, { useState } from 'react';
import { format } from 'date-fns';
import { fr } from 'date-fns/locale';
import { FileText, Clock } from 'lucide-react';
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from '@/components/ui/card';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import ActesList from './ActesList';
import IndexDetailModal from './IndexDetailModal';

interface IndexEntryProps {
  index: any;
  indexNumber: number;
  actes: any[];
}

const IndexEntry: React.FC<IndexEntryProps> = ({ index, indexNumber, actes }) => {
  const [isIndexModalOpen, setIsIndexModalOpen] = useState(false);
  
  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), 'PPP', { locale: fr });
    } catch (error) {
      return 'Date invalide';
    }
  };

  // Helper function to determine status badge styling
  const getStatusBadgeClass = (status: string) => {
    const lowerStatus = status?.toLowerCase() || '';
    
    if (lowerStatus.includes('completed')) {
      return "bg-green-100 text-green-800 border-green-200";
    } else if (lowerStatus.includes('progress') || lowerStatus.includes('phase')) {
      return "bg-blue-100 text-blue-800 border-blue-200";
    } else if (lowerStatus.includes('error') || lowerStatus.includes('not available')) {
      return "bg-red-100 text-red-800 border-red-200";
    } else if (lowerStatus.includes('pending')) {
      return "bg-yellow-100 text-yellow-800 border-yellow-200";
    } else {
      return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  return (
    <>
      <Card className="border-l-[5px] overflow-hidden hover:shadow-md transition-shadow cursor-pointer relative" 
        style={{ borderLeftColor: '#3E5AB3' }}
        onClick={() => setIsIndexModalOpen(true)}
      >
        <CardHeader className="pb-3 bg-muted/20">
          <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start gap-2">
            <CardTitle className="text-lg font-semibold flex flex-wrap items-center gap-2">
              <span className="mr-1">Index {indexNumber} - {index.lot_number}</span>
              <span className="text-xs px-2 py-1 rounded-full bg-muted text-muted-foreground whitespace-nowrap">
                {index.cadastre || 'Standard'}
              </span>
              {index.doc_number && (
                <span className="text-xs px-2 py-1 rounded-full bg-blue-100 text-blue-800 whitespace-nowrap">
                  Document #{index.doc_number}
                </span>
              )}
            </CardTitle>
            
            {index.status && (
              <span className={`text-xs px-2.5 py-0.5 rounded-full border ${getStatusBadgeClass(index.status)} whitespace-nowrap`}>
                {index.status}
              </span>
            )}
          </div>
          <CardDescription className="flex items-center mt-1">
            <Clock className="h-3.5 w-3.5 mr-1.5" />
            Créé le {formatDate(index.created_at || '')}
          </CardDescription>
        </CardHeader>
        
        <CardContent className="pt-4">
          <Accordion type="single" collapsible className="w-full" onClick={(e) => e.stopPropagation()}>
            <AccordionItem value="actes" className="border-b-0">
              <AccordionTrigger className="py-3 hover:no-underline">
                <div className="flex items-center px-2 py-1 bg-accent/30 rounded-md">
                  <FileText className="h-4 w-4 mr-2" />
                  <span className="font-medium">
                    Inscriptions ({actes?.length || 0})
                  </span>
                </div>
              </AccordionTrigger>
              <AccordionContent>
                <div className="mt-3">
                  <ActesList actes={actes} />
                </div>
              </AccordionContent>
            </AccordionItem>
          </Accordion>
        </CardContent>
      </Card>
      
      <IndexDetailModal 
        index={index} 
        open={isIndexModalOpen} 
        onOpenChange={setIsIndexModalOpen} 
      />
    </>
  );
};

export default IndexEntry;
