
import React from 'react';
import { format } from 'date-fns';
import { fr } from 'date-fns/locale';
import { 
  FileText, 
  Calendar, 
  User, 
  ExternalLink, 
  MapPin,
  Radiation
} from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

interface ActeDetailModalProps {
  acte: any;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

const ActeDetailModal: React.FC<ActeDetailModalProps> = ({ 
  acte, 
  open, 
  onOpenChange 
}) => {
  if (!acte) return null;

  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), 'PPP', { locale: fr });
    } catch (error) {
      return 'Date invalide';
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl h-[85vh] flex flex-col p-0 gap-0 overflow-hidden">
        <DialogHeader className="px-6 py-4 border-b bg-muted/30 flex-shrink-0">
          <div className="flex items-center gap-2">
            <div 
              className="h-3 w-3 rounded-full bg-primary/80" 
              aria-hidden="true"
            />
            <DialogTitle className="text-xl flex items-center gap-2">
              Détails de l'inscription
              {acte.is_radiated && (
                <div className="flex items-center gap-1.5">
                  <Radiation className="h-4 w-4 text-red-500" />
                  <span className="text-sm text-red-500 font-normal">Acte radiée</span>
                </div>
              )}
            </DialogTitle>
          </div>
          <div className="flex items-center gap-2 mt-1 text-sm text-muted-foreground">
            {acte.acte_nature && (
              <Badge variant="secondary" className="font-normal">
                {acte.acte_nature}
              </Badge>
            )}
            {acte.acte_publication_date && (
              <span className="text-xs text-muted-foreground flex items-center">
                <Calendar className="h-3 w-3 mr-1" />
                {formatDate(acte.acte_publication_date)}
              </span>
            )}
          </div>
        </DialogHeader>
        
        <ScrollArea className="flex-1">
          <div className="p-6 space-y-6">
            <Card className="overflow-hidden border-border/50 shadow-sm bg-card/50">
              <CardContent className="p-0">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-0">
                  <div className="p-4 space-y-4 border-b md:border-b-0 md:border-r border-border/30">
                    {acte.acte_publication_date && (
                      <div>
                        <h3 className="text-sm font-medium text-muted-foreground mb-1">Date de publication</h3>
                        <div className="flex items-center">
                          <Calendar className="h-3.5 w-3.5 mr-1.5 text-muted-foreground" />
                          <span>{formatDate(acte.acte_publication_date)}</span>
                        </div>
                      </div>
                    )}

                    {acte.acte_notary_minute && (
                      <div>
                        <h3 className="text-sm font-medium text-muted-foreground mb-1">Notaire et minute</h3>
                        <p className="font-medium">{acte.acte_notary_minute}</p>
                      </div>
                    )}
                    
                    {acte.acte_nature && (
                      <div>
                        <h3 className="text-sm font-medium text-muted-foreground mb-1">Nature de l'acte</h3>
                        <p className="font-medium">{acte.acte_nature}</p>
                      </div>
                    )}
                    
                    {acte.acte_publication_number && (
                      <div>
                        <h3 className="text-sm font-medium text-muted-foreground mb-1">Numéro de publication</h3>
                        <p className="font-medium">{acte.acte_publication_number}</p>
                      </div>
                    )}
                  </div>
                  
                  <div className="p-4 space-y-4">
                    {acte.acte_notary && (
                      <div>
                        <h3 className="text-sm font-medium text-muted-foreground mb-1">Notaire</h3>
                        <div className="flex items-center">
                          <User className="h-3.5 w-3.5 mr-1.5 text-muted-foreground" />
                          <span>{acte.acte_notary}</span>
                        </div>
                      </div>
                    )}
                    
                    {acte.circonscription_fonciere && (
                      <div>
                        <h3 className="text-sm font-medium text-muted-foreground mb-1">Circonscription foncière</h3>
                        <div className="flex items-center">
                          <MapPin className="h-3.5 w-3.5 mr-1.5 text-muted-foreground" />
                          <span>{acte.circonscription_fonciere}</span>
                        </div>
                      </div>
                    )}
                    
                    {acte.created_at && (
                      <div>
                        <h3 className="text-sm font-medium text-muted-foreground mb-1">Ajouté le</h3>
                        <div className="flex items-center">
                          <Calendar className="h-3.5 w-3.5 mr-1.5 text-muted-foreground" />
                          <span>{formatDate(acte.created_at)}</span>
                        </div>
                      </div>
                    )}

                    {acte.is_radiated && acte.radiation_number && (
                      <div>
                        <h3 className="text-sm font-medium text-muted-foreground mb-1">Numéro de radiation</h3>
                        <p className="font-medium">{acte.radiation_number}</p>
                      </div>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
            
            {acte.acte_details && (
              <div>
                <h3 className="text-sm font-medium text-muted-foreground mb-2">Détails</h3>
                <div className="p-4 bg-muted/30 rounded-lg border border-border/30">
                  <p className="text-sm whitespace-pre-line">{acte.acte_details}</p>
                </div>
              </div>
            )}
            
            {acte.acte_parties && (
              <div>
                <h3 className="text-sm font-medium text-muted-foreground mb-2">Parties impliquées</h3>
                <div className="p-4 bg-muted/30 rounded-lg border border-border/30">
                  <p className="text-sm whitespace-pre-line">{acte.acte_parties}</p>
                </div>
              </div>
            )}

            {acte.matrimonie && (
              <div>
                <h3 className="text-sm font-medium text-muted-foreground mb-2">Matrimonie</h3>
                <div className="p-4 bg-muted/30 rounded-lg border border-border/30">
                  <p className="text-sm whitespace-pre-line">{acte.matrimonie}</p>
                </div>
              </div>
            )}
            
            {acte.acte_summary && (
              <div>
                <h3 className="text-sm font-medium text-muted-foreground mb-2">Résumé</h3>
                <div className="p-4 bg-muted/30 rounded-lg border border-border/30">
                  <p className="text-sm whitespace-pre-line">{acte.acte_summary}</p>
                </div>
              </div>
            )}

            {acte.writting && (
              <div>
                <h3 className="text-sm font-medium text-muted-foreground mb-2">Version finale</h3>
                <div className="p-4 bg-muted/30 rounded-lg border border-border/30">
                  <p className="text-sm whitespace-pre-line">{acte.writting}</p>
                </div>
              </div>
            )}
            
            {acte.doc_url && (
              <div>
                <h3 className="text-sm font-medium text-muted-foreground mb-2">Document</h3>
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="flex items-center" 
                  asChild
                >
                  <a 
                    href={acte.doc_url} 
                    target="_blank" 
                    rel="noopener noreferrer"
                  >
                    <FileText className="h-4 w-4 mr-2" />
                    Voir le document
                    <ExternalLink className="h-3 w-3 ml-1.5" />
                  </a>
                </Button>
              </div>
            )}
          </div>
        </ScrollArea>
      </DialogContent>
    </Dialog>
  );
};

export default ActeDetailModal;
