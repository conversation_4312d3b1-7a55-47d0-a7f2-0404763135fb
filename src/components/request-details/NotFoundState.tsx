
import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { ChevronLeft } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';

const NotFoundState: React.FC = () => {
  return (
    <div className="min-h-screen flex flex-col bg-background">
      <main className="flex-1 container px-4 sm:px-6 lg:px-8 py-6 ml-[70px]">
        <div className="flex flex-col items-center justify-center h-full space-y-4">
          <h2 className="text-2xl font-semibold">Demande non trouvée</h2>
          <p className="text-muted-foreground">La demande que vous recherchez n'existe pas ou a été supprimée.</p>
          <Link to="/">
            <Button>
              <ChevronLeft className="mr-2 h-4 w-4" />
              Retour à la liste des demandes
            </Button>
          </Link>
        </div>
      </main>
    </div>
  );
};

export default NotFoundState;
