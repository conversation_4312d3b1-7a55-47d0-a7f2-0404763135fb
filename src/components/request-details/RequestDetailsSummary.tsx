
import React from 'react';
import { format } from 'date-fns';
import { fr } from 'date-fns/locale';
import { Card } from '@/components/ui/card';
import { FileText, Calendar, ClipboardList, AlertCircle } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Badge } from '@/components/ui/badge';

interface RequestDetailsSummaryProps {
  request: any;
  indexCount: number;
  acteCount: number;
}

const RequestDetailsSummary: React.FC<RequestDetailsSummaryProps> = ({ 
  request, 
  indexCount, 
  acteCount 
}) => {
  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), 'PPP', { locale: fr });
    } catch (error) {
      return 'Date invalide';
    }
  };

  // Status translation function - Updated with exact mapping
  const translateStatus = (status: string): string => {
    switch (status?.toLowerCase()) {
      case 'pending':
        return 'En attente';
      case 'inprogress':
      case 'in progress':
        return 'En cours';
      case 'processing_phase_1':
      case 'phase 1':
        return 'Phase 1';
      case 'processing_phase_2':
      case 'phase 2':
        return 'Phase 2';
      case 'processing_phase_3':
      case 'phase 3':
        return 'Phase 3';
      case 'completed':
        return 'Complété';
      case 'finalizing':
        return 'Finalisation';
      case 'error':
        return 'Erreur';
      default:
        return status || 'Inconnu';
    }
  };

  // Status badge variant based on status
  const getStatusVariant = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'completed':
        return 'completed';
      case 'error':
        return 'error';
      case 'finalizing':
        return 'finalizing';
      case 'pending':
        return 'pending';
      case 'inprogress':
      case 'in progress':
        return 'in-progress';
      case 'processing_phase_1':
      case 'phase 1':
        return 'phase1';
      case 'processing_phase_2':
      case 'phase 2':
        return 'phase2';
      case 'processing_phase_3':
      case 'phase 3':
        return 'phase3';
      default:
        return 'secondary';
    }
  };

  return (
    <div className="bg-gradient-to-r from-slate-50 to-blue-50 dark:from-slate-900 dark:to-blue-950 rounded-xl border border-blue-100 dark:border-blue-950 shadow-sm p-6">
      <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-4">
        <div className="flex items-center">
          <div className="h-14 w-14 rounded-lg bg-primary/10 flex items-center justify-center mr-4">
            <FileText className="h-7 w-7 text-primary" />
          </div>
          <div>
            <div className="flex items-center gap-2 mb-1">
              <h1 className="text-2xl font-bold">Demande #{request.id.substring(0, 8)}</h1>
            </div>
            <p className="text-muted-foreground flex items-center">
              <Calendar className="h-4 w-4 mr-1.5" /> 
              Créée le {formatDate(request.created_at)}
              {request.completed_at && (
                <>
                  <span className="mx-1.5">•</span>
                  <AlertCircle className="h-4 w-4 mr-1.5" />
                  Complétée le {formatDate(request.completed_at)}
                </>
              )}
            </p>
          </div>
        </div>

        <div className="flex flex-wrap gap-3 items-center">
          <div className="bg-background/80 border border-border/40 rounded-lg py-2 px-4 flex items-center space-x-2">
            <ClipboardList className="h-4 w-4 text-muted-foreground" />
            <span className="text-sm">
              <span className="font-medium">{indexCount}</span> index trouvé{indexCount !== 1 ? 's' : ''}
            </span>
          </div>
          
          <div className="bg-background/80 border border-border/40 rounded-lg py-2 px-4 flex items-center space-x-2">
            <FileText className="h-4 w-4 text-muted-foreground" />
            <span className="text-sm">
              <span className="font-medium">{acteCount}</span> inscription{acteCount !== 1 ? 's' : ''}
            </span>
          </div>
          
          {/* Status badge */}
          {request.status && (
            <Badge variant={getStatusVariant(request.status)} className="text-xs">
              {translateStatus(request.status)}
            </Badge>
          )}
        </div>
      </div>
    </div>
  );
};

export default RequestDetailsSummary;
