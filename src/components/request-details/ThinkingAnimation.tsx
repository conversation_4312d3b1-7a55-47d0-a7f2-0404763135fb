
import React, { useEffect, useState } from 'react';

const ThinkingAnimation = () => {
  const [dots, setDots] = useState('');

  useEffect(() => {
    const interval = setInterval(() => {
      setDots(prevDots => {
        if (prevDots === '...') return '.';
        if (prevDots === '..') return '...';
        if (prevDots === '.') return '..';
        return '.';
      });
    }, 500);

    return () => clearInterval(interval);
  }, []);

  return (
    <div className="py-2 px-4">
      <p className="text-muted-foreground italic">
        Je réfléchis{dots}
      </p>
    </div>
  );
};

export default ThinkingAnimation;
