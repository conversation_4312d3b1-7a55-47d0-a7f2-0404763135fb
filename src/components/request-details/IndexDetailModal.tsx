
import React from 'react';
import { format } from 'date-fns';
import { fr } from 'date-fns/locale';
import { FileText, Calendar, AlertCircle, ExternalLink } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';

interface IndexDetailModalProps {
  index: any;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

const IndexDetailModal: React.FC<IndexDetailModalProps> = ({ 
  index, 
  open, 
  onOpenChange 
}) => {
  if (!index) return null;

  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), 'PPP', { locale: fr });
    } catch (error) {
      return 'Date invalide';
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl h-[85vh] flex flex-col p-0 gap-0 overflow-hidden">
        <DialogHeader className="px-6 py-4 border-b bg-muted/30 flex-shrink-0">
          <div className="flex items-center gap-2">
            <div 
              className="h-3 w-3 rounded-full bg-primary" 
              aria-hidden="true"
            />
            <DialogTitle className="text-xl">
              Index {index.lot_number}
            </DialogTitle>
          </div>
          <div className="flex items-center mt-1 text-sm text-muted-foreground">
            Document #{index.doc_number || 'N/A'}
          </div>
        </DialogHeader>
        
        <ScrollArea className="flex-1">
          <div className="p-6 space-y-6">
            <Card className="overflow-hidden border-border/50 shadow-sm bg-card/50">
              <CardContent className="p-0">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-0">
                  <div className="p-4 space-y-4 border-b md:border-b-0 md:border-r border-border/30">
                    <div>
                      <h3 className="text-sm font-medium text-muted-foreground mb-1">Numéro de lot</h3>
                      <p className="font-medium">{index.lot_number || 'Non spécifié'}</p>
                    </div>
                    
                    <div>
                      <h3 className="text-sm font-medium text-muted-foreground mb-1">Cadastre</h3>
                      <p className="font-medium">{index.cadastre || 'Non spécifié'}</p>
                    </div>
                    
                    <div>
                      <h3 className="text-sm font-medium text-muted-foreground mb-1">Circonscription</h3>
                      <p className="font-medium">{index.circonscription || 'Non spécifiée'}</p>
                    </div>
                  </div>
                  
                  <div className="p-4 space-y-4">
                    <div>
                      <h3 className="text-sm font-medium text-muted-foreground mb-1">Créé le</h3>
                      <div className="flex items-center">
                        <Calendar className="h-3.5 w-3.5 mr-1.5 text-muted-foreground" />
                        <span>{formatDate(index.created_at || '')}</span>
                      </div>
                    </div>
                    
                    <div>
                      <h3 className="text-sm font-medium text-muted-foreground mb-1">Nombre d'inscriptions</h3>
                      <div className="flex items-center">
                        <FileText className="h-3.5 w-3.5 mr-1.5 text-muted-foreground" />
                        <span>{index.number_of_actes || 0}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            {index.index_summary && (
              <div>
                <h3 className="text-sm font-medium text-muted-foreground mb-2">Résumé</h3>
                <div className="p-4 bg-muted/30 rounded-lg border border-border/30">
                  <p className="text-sm whitespace-pre-line">{index.index_summary}</p>
                </div>
              </div>
            )}
            
            {index.relevance_explanation && (
              <div>
                <h3 className="text-sm font-medium text-muted-foreground mb-2">Explication de pertinence</h3>
                <div className="p-4 bg-muted/30 rounded-lg border border-border/30">
                  <div className="flex items-start">
                    <AlertCircle className="h-4 w-4 mr-2 mt-0.5 text-muted-foreground" />
                    <p className="text-sm whitespace-pre-line">{index.relevance_explanation}</p>
                  </div>
                </div>
              </div>
            )}
            
            {index.doc_url && (
              <div>
                <h3 className="text-sm font-medium text-muted-foreground mb-2">Document</h3>
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="flex items-center" 
                  asChild
                >
                  <a 
                    href={index.doc_url} 
                    target="_blank" 
                    rel="noopener noreferrer"
                  >
                    <FileText className="h-4 w-4 mr-2" />
                    Voir le document
                    <ExternalLink className="h-3 w-3 ml-1.5" />
                  </a>
                </Button>
              </div>
            )}
          </div>
        </ScrollArea>
      </DialogContent>
    </Dialog>
  );
};

export default IndexDetailModal;
