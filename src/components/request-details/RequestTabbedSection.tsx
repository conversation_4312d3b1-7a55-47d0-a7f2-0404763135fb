
import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent } from '@/components/ui/tabs';
import { supabase } from '@/lib/supabase';
import ReactMarkdown from 'react-markdown';

interface RequestTabbedSectionProps {
  requestId: string;
}

interface RequestData {
  servitudes: string | null;
  regimes_matrimoniaux: string | null;
  erreurs: string | null;
  autres_considerations: string | null;
}

const RequestTabbedSection: React.FC<RequestTabbedSectionProps> = ({ requestId }) => {
  const [requestData, setRequestData] = useState<RequestData | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchRequestData = async () => {
      try {
        const { data, error } = await supabase
          .from('requests')
          .select('servitudes, regimes_matrimoniaux, erreurs, autres_considerations')
          .eq('id', requestId)
          .single();
        
        if (error) throw error;
        
        setRequestData(data);
      } catch (error) {
        console.error('Error fetching request highlights:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchRequestData();
  }, [requestId]);

  const renderMarkdownContent = (content: string | null) => {
    if (!content) {
      return (
        <p className="text-muted-foreground italic text-center py-4">
          Aucune information disponible
        </p>
      );
    }

    return (
      <div className="markdown-content">
        <ReactMarkdown>
          {content}
        </ReactMarkdown>
      </div>
    );
  };

  if (loading) {
    return (
      <div className="bg-card rounded-xl border border-border/40 shadow-sm overflow-hidden">
        <div className="border-b border-border/40 px-6 py-4">
          <h2 className="text-xl font-semibold">Faits saillants</h2>
        </div>
        <div className="p-6 flex items-center justify-center">
          <p className="text-muted-foreground">Chargement...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-card rounded-xl border border-border/40 shadow-sm overflow-hidden">
      <div className="border-b border-border/40 px-6 py-4">
        <h2 className="text-xl font-semibold">Faits saillants</h2>
      </div>
      <div className="p-6">
        <Tabs defaultValue="servitudes">
          <TabsList className="mb-4">
            <TabsTrigger value="servitudes">Servitudes</TabsTrigger>
            <TabsTrigger value="regimes">Régimes matrimoniaux</TabsTrigger>
            <TabsTrigger value="erreurs">Erreurs</TabsTrigger>
            <TabsTrigger value="autres">Autres considérations</TabsTrigger>
          </TabsList>
          
          <TabsContent value="servitudes" className="p-4 border rounded-md">
            {renderMarkdownContent(requestData?.servitudes)}
          </TabsContent>
          
          <TabsContent value="regimes" className="p-4 border rounded-md">
            {renderMarkdownContent(requestData?.regimes_matrimoniaux)}
          </TabsContent>
          
          <TabsContent value="erreurs" className="p-4 border rounded-md">
            {renderMarkdownContent(requestData?.erreurs)}
          </TabsContent>
          
          <TabsContent value="autres" className="p-4 border rounded-md">
            {renderMarkdownContent(requestData?.autres_considerations)}
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default RequestTabbedSection;
