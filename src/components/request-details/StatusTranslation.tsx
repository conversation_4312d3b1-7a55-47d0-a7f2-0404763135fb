
import React from 'react';
import { Badge } from '@/components/ui/badge';

/**
 * @deprecated This component is no longer in use as we've removed all status indicators from the UI.
 * It's kept here for reference but should not be used.
 */

interface StatusTranslationProps {
  status: string;
  type: 'index' | 'acte' | 'request';
  className?: string;
}

const StatusTranslation: React.FC<StatusTranslationProps> = ({ status, type, className = '' }) => {
  console.warn('StatusTranslation component is deprecated and should not be used');
  return null;
};

export default StatusTranslation;
