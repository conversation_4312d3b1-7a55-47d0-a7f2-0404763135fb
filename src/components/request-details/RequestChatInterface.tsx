import React, { useState, useEffect, useRef, useCallback } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/context/AuthContext';
import ReactMarkdown from 'react-markdown';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { cn } from '@/lib/utils';
import { formatDistanceToNow } from 'date-fns';
import ThinkingAnimation from './ThinkingAnimation';

interface ChatMessage {
  id: string;
  chat_session_id: string;
  request_id: string;
  sender: 'user' | 'bot' | 'assistant';
  content: string;
  user_id?: string;
  created_at: string;
  metadata?: Record<string, any>;
}

interface ChatSession {
  id: string;
  created_at: string;
  metadata?: Record<string, any>;
}

interface RequestChatInterfaceProps {
  requestId: string;
}

const RequestChatInterface: React.FC<RequestChatInterfaceProps> = ({ requestId }) => {
  const { user } = useAuth();
  const [chatSessionId, setChatSessionId] = useState<string | null>(null);
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [loadingSession, setLoadingSession] = useState(true);
  const [loadingMessages, setLoadingMessages] = useState(false);
  const [sendingMessage, setSendingMessage] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [hasUserSentMessage, setHasUserSentMessage] = useState(false);
  const [isWaitingForResponse, setIsWaitingForResponse] = useState(false);
  const scrollAreaRef = useRef<HTMLDivElement>(null);
  const messageEndRef = useRef<HTMLDivElement>(null);
  const initialScrollDone = useRef<boolean>(false);

  // Enhanced scroll to bottom function that works more reliably
  const scrollToBottom = useCallback(() => {
    // Use requestAnimationFrame to ensure DOM has been updated
    requestAnimationFrame(() => {
      if (messageEndRef.current) {
        messageEndRef.current.scrollIntoView({ behavior: 'smooth' });
      } else if (scrollAreaRef.current) {
        scrollAreaRef.current.scrollTop = scrollAreaRef.current.scrollHeight;
      }
    });
  }, []);

  // Call scroll to bottom whenever messages change
  useEffect(() => {
    if (messages.length > 0) {
      scrollToBottom();
    }
  }, [messages, scrollToBottom]);

  // This effect will force scroll to bottom once messages are loaded and component is mounted
  useEffect(() => {
    // Only run on initial messages load
    if (messages.length > 0 && !initialScrollDone.current && !loadingMessages) {
      console.log("Executing initial scroll to bottom");
      initialScrollDone.current = true;
      
      // Use multiple attempts with increasing delays for better reliability
      const scrollAttempts = [0, 100, 300, 500];
      
      scrollAttempts.forEach(delay => {
        setTimeout(() => {
          if (messageEndRef.current) {
            messageEndRef.current.scrollIntoView({ behavior: delay === 0 ? 'auto' : 'smooth' });
          } else if (scrollAreaRef.current) {
            scrollAreaRef.current.scrollTop = scrollAreaRef.current.scrollHeight;
          }
        }, delay);
      });
    }
  }, [messages, loadingMessages]);

  const findOrCreateChatSession = useCallback(async () => {
    if (!requestId) return;
    setLoadingSession(true);
    setError(null);
    console.log(`Looking for chat session for request ID: ${requestId}`);

    try {
      const { data: existingSession, error: findError } = await supabase
        .from('chat_sessions')
        .select('id')
        .eq('metadata->>related_request_id', requestId)
        .maybeSingle();

      if (findError) {
        console.error('Error finding chat session:', findError);
        throw new Error(`Failed to find chat session: ${findError.message}`);
      }

      if (existingSession) {
        console.log(`Found existing chat session: ${existingSession.id}`);
        setChatSessionId(existingSession.id);
      } else {
        if (!user) {
          console.error("User not authenticated. Cannot create chat session.");
          throw new Error("User not authenticated. Cannot create chat session.");
        }
        const { data: newSession, error: createError } = await supabase
          .from('chat_sessions')
          .insert({
            metadata: { related_request_id: requestId },
            user_id: user.id
          })
          .select('id')
          .single();

        if (createError) {
          console.error('Error creating chat session:', createError);
          throw new Error(`Failed to create chat session: ${createError.message}`);
        }

        if (newSession) {
          console.log(`Created new chat session: ${newSession.id}`);
          setChatSessionId(newSession.id);
        } else {
          throw new Error('Failed to create chat session, no ID returned.');
        }
      }
    } catch (err: any) {
      console.error('Error in findOrCreateChatSession:', err);
      setError(err.message || 'An unexpected error occurred while setting up the chat session.');
    } finally {
      setLoadingSession(false);
    }
  }, [requestId]);

  const fetchMessages = useCallback(async () => {
    if (!chatSessionId) return;
    setLoadingMessages(true);
    setError(null);
    console.log(`Fetching messages for session ID: ${chatSessionId}`);

    try {
      const { data, error: fetchError } = await supabase
        .from('messages')
        .select('id, chat_session_id, request_id, sender, content, user_id, created_at, metadata')
        .eq('chat_session_id', chatSessionId)
        .order('created_at', { ascending: true });

      if (fetchError) {
        console.error('Error fetching messages:', fetchError);
        throw new Error(`Failed to fetch messages: ${fetchError.message}`);
      }

      console.log(`Fetched ${data?.length ?? 0} messages.`);
      setMessages((data as ChatMessage[]) || []);
      // Scroll to bottom after messages are loaded
      scrollToBottom();
    } catch (err: any) {
      console.error('Error in fetchMessages:', err);
      setError(err.message || 'An unexpected error occurred while fetching messages.');
    } finally {
      setLoadingMessages(false);
    }
  }, [chatSessionId, scrollToBottom]);

  const handleSendMessage = async (e?: React.FormEvent) => {
    e?.preventDefault();
    if (!newMessage.trim() || !chatSessionId || !user || sendingMessage) return;

    setSendingMessage(true);
    setIsWaitingForResponse(true);
    setError(null);
    const contentToSend = newMessage.trim();
    const traceId = crypto.randomUUID();
    console.log(`Sending message for session ${chatSessionId}, request ${requestId}, trace ${traceId}`);

    try {
      const { error: insertError } = await supabase
        .from('messages')
        .insert({
          chat_session_id: chatSessionId,
          request_id: requestId,
          sender: 'user',
          content: contentToSend,
          user_id: user.id,
          metadata: { trace_id: traceId }
        });

      if (insertError) {
        console.error('Error sending message:', insertError);
        throw new Error(`Failed to send message: ${insertError.message}`);
      }

      console.log('Message sent successfully.');
      setNewMessage('');
      // Scroll to bottom after sending a new message
      scrollToBottom();
    } catch (err: any) {
      console.error('Error in handleSendMessage:', err);
      setError(err.message || 'An unexpected error occurred while sending the message.');
    } finally {
      setSendingMessage(false);
    }
  };

  useEffect(() => {
    findOrCreateChatSession();
  }, [findOrCreateChatSession]);

  useEffect(() => {
    if (chatSessionId) {
      fetchMessages();

      console.log(`Setting up real-time subscription for session: ${chatSessionId}`);
      const channel = supabase.channel(`chat-session-${chatSessionId}`)
        .on<ChatMessage>(
          'postgres_changes',
          {
            event: 'INSERT',
            schema: 'public',
            table: 'messages',
            filter: `chat_session_id=eq.${chatSessionId}`
          },
          (payload) => {
            console.log('Real-time: New message received:', payload.new);
            const newMessagePayload = payload.new as ChatMessage;
            setMessages((currentMessages) => {
              if (currentMessages.some(msg => msg.id === newMessagePayload.id)) {
                return currentMessages;
              }
              return [...currentMessages, newMessagePayload];
            });
            // Scroll to bottom when new message is received via real-time subscription
            scrollToBottom();
          }
        )
        .subscribe((status, err) => {
          if (status === 'SUBSCRIBED') {
            console.log(`Successfully subscribed to channel chat-session-${chatSessionId}`);
          } else if (status === 'CHANNEL_ERROR' || status === 'TIMED_OUT') {
            console.error(`Subscription error on channel chat-session-${chatSessionId}:`, err);
            setError(`Real-time connection error: ${err?.message || 'Failed to subscribe'}`);
          } else if (status === 'CLOSED') {
            console.log(`Subscription closed for channel chat-session-${chatSessionId}`);
          }
        });

      return () => {
        console.log(`Unsubscribing from channel chat-session-${chatSessionId}`);
        supabase.removeChannel(channel).catch(err => {
          console.error("Error removing channel:", err);
        });
      };
    }
  }, [chatSessionId, fetchMessages, scrollToBottom]);

  useEffect(() => {
    if (!hasUserSentMessage && messages.some(msg => msg.sender === 'user')) {
      setHasUserSentMessage(true);
    }
  }, [messages, hasUserSentMessage]);

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Chat</CardTitle>
      </CardHeader>
      <CardContent className="pr-0 h-[400px]">
        {(loadingSession || loadingMessages) ? (
          <div className="flex items-center justify-center h-full">
            <p className="text-center text-muted-foreground">
              {loadingSession ? 'Initialisation de la session de chat...' : 'Chargement des messages...'}
            </p>
          </div>
        ) : !hasUserSentMessage ? (
          <div className="flex items-center justify-center h-full">
            <p className="text-center text-muted-foreground">Envoyez un message pour démarrer la conversation.</p>
          </div>
        ) : (
          <ScrollArea className="h-full w-full pr-6" ref={scrollAreaRef}>
            {messages.length > 0 ? (
              messages.map((message) => (
                <div
                  key={message.id}
                  className={cn(
                    'mb-4 flex items-end gap-2',
                    message.sender === 'user' ? 'justify-end' : 'justify-start'
                  )}
                >
                  {message.sender !== 'user' && (
                    <Avatar className="h-8 w-8">
                      <AvatarFallback>B</AvatarFallback>
                    </Avatar>
                  )}
                  <div
                    className={cn(
                      'max-w-[75%] rounded-lg p-3 text-sm',
                      message.sender === 'user'
                        ? 'bg-primary text-primary-foreground'
                        : 'bg-muted'
                    )}
                  >
                    {message.sender === 'user' ? (
                      <p className="whitespace-pre-wrap">{message.content}</p>
                    ) : (
                      <ReactMarkdown
                        components={{
                          p: ({ node, ...props }) => (
                            <p className="my-1 leading-normal" {...props} />
                          ),
                          h1: ({ node, ...props }) => (
                            <h1 className="mt-2 mb-1 text-foreground text-xl font-bold" {...props} />
                          ),
                          h2: ({ node, ...props }) => (
                            <h2 className="mt-2 mb-1 text-foreground text-lg font-bold" {...props} />
                          ),
                          h3: ({ node, ...props }) => (
                            <h3 className="mt-2 mb-1 text-foreground text-md font-bold" {...props} />
                          ),
                          code: ({ ...props }) => {
                            const isInline = !props.className;
                            return isInline ? (
                              <code className="bg-muted-foreground/20 p-1 rounded" {...props} />
                            ) : (
                              <code className="bg-muted-foreground/20 p-1 rounded" {...props} />
                            );
                          },
                          pre: ({ node, ...props }) => (
                            <pre className="bg-muted-foreground/20 p-2 rounded overflow-x-auto my-2" {...props} />
                          ),
                          ul: ({ node, ...props }) => (
                            <ul className="list-disc pl-6 my-2" {...props} />
                          ),
                          ol: ({ node, ...props }) => (
                            <ol className="list-decimal pl-6 my-2" {...props} />
                          ),
                          li: ({ node, ...props }) => (
                            <li className="my-1" {...props} />
                          ),
                          a: ({ node, ...props }) => (
                            <a className="text-blue-500 hover:underline" {...props} />
                          ),
                          blockquote: ({ node, ...props }) => (
                            <blockquote className="border-l-4 border-muted-foreground/50 pl-4 italic my-2" {...props} />
                          ),
                        }}
                      >
                        {message.content}
                      </ReactMarkdown>
                    )}
                    <p className={cn(
                      "mt-1 text-xs",
                      message.sender === 'user' ? 'text-primary-foreground/80 text-right' : 'text-muted-foreground text-left'
                    )}>
                      {formatDistanceToNow(new Date(message.created_at), { addSuffix: true })}
                    </p>
                  </div>
                  {message.sender === 'user' && user && (
                    <Avatar className="h-8 w-8">
                      <AvatarImage src={user.user_metadata?.avatar_url} alt={user.email} />
                      <AvatarFallback>{user.email?.[0].toUpperCase()}</AvatarFallback>
                    </Avatar>
                  )}
                </div>
              ))
            ) : null}
            {/* This invisible div serves as a marker for scrolling to the bottom */}
            <div ref={messageEndRef} style={{ height: '1px', width: '100%' }} />
            {isWaitingForResponse && (
              <div className="mt-4">
                <ThinkingAnimation />
              </div>
            )}
            {error && (
              <p className="text-center text-red-500 text-sm mt-4 px-6">
                Erreur: {error}
              </p>
            )}
          </ScrollArea>
        )}
      </CardContent>
      <CardFooter>
        <form onSubmit={handleSendMessage} className="flex w-full items-center space-x-2">
          <Input
            id="message"
            placeholder="Écrivez votre message..."
            className="flex-1"
            autoComplete="off"
            value={newMessage}
            onChange={(event) => setNewMessage(event.target.value)}
            disabled={loadingSession || sendingMessage || !!error}
          />
          <Button type="submit" size="icon" disabled={!newMessage.trim() || loadingSession || sendingMessage || !!error}>
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-4 w-4"><path d="m22 2-7 20-4-9-9-4Z"/><path d="m22 2-11 11"/></svg>
            <span className="sr-only">Envoyer</span>
          </Button>
        </form>
      </CardFooter>
    </Card>
  );
};

export default RequestChatInterface;
