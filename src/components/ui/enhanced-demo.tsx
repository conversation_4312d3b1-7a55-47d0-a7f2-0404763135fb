import React from 'react';
import { Enhanced<PERSON><PERSON>, EnhancedCardHeader, EnhancedCard<PERSON><PERSON>le, EnhancedCardContent } from './enhanced-card';
import { EnhancedButton } from './enhanced-button';
import { EnhancedBadge } from './enhanced-badge';
import StatusCard from '../dashboard/StatusCard';
import { 
  FileText, 
  Users, 
  TrendingUp, 
  CheckCircle, 
  Clock, 
  AlertTriangle,
  Star,
  Download,
  Plus,
  Sparkles
} from 'lucide-react';

const EnhancedUIDemo: React.FC = () => {
  return (
    <div className="p-8 space-y-8 min-h-screen">
      {/* Header Section */}
      <div className="text-center space-y-4">
        <h1 className="text-4xl font-bold gradient-text">Enhanced UI Components</h1>
        <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
          A showcase of the new design system with glassmorphism, semantic colors, and delightful micro-interactions.
        </p>
      </div>

      {/* Status Cards Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatusCard
          title="Total Requests"
          value={324}
          icon={<FileText className="w-5 h-5" />}
          trend="up"
          trendValue="+12%"
          variant="default"
        />
        <StatusCard
          title="Active Users"
          value={89}
          icon={<Users className="w-5 h-5" />}
          trend="up"
          trendValue="+5%"
          variant="glass"
        />
        <StatusCard
          title="Completed"
          value={256}
          icon={<CheckCircle className="w-5 h-5" />}
          trend="neutral"
          trendValue="0%"
          variant="gradient"
          gradientClass="from-success to-success-600"
        />
        <StatusCard
          title="Pending"
          value={68}
          icon={<Clock className="w-5 h-5" />}
          trend="down"
          trendValue="-3%"
          variant="gradient"
          gradientClass="from-warning to-warning-600"
        />
      </div>

      {/* Enhanced Cards Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Glass Card */}
        <EnhancedCard variant="glass" hover="lift" padding="lg">
          <EnhancedCardHeader icon={<Sparkles className="w-5 h-5" />}>
            <EnhancedCardTitle gradient>Glassmorphism Card</EnhancedCardTitle>
            <p className="text-sm text-muted-foreground">
              Beautiful glass effect with backdrop blur
            </p>
          </EnhancedCardHeader>
          <EnhancedCardContent>
            <div className="space-y-4">
              <p className="text-sm">
                This card features glassmorphism design with subtle transparency and backdrop blur effects.
              </p>
              <EnhancedButton variant="glass" icon={<Star className="w-4 h-4" />}>
                Glass Button
              </EnhancedButton>
            </div>
          </EnhancedCardContent>
        </EnhancedCard>

        {/* Interactive Card */}
        <EnhancedCard variant="interactive" hover="glow" padding="lg">
          <EnhancedCardHeader 
            icon={<TrendingUp className="w-5 h-5" />}
            badge={<EnhancedBadge variant="success" icon={<CheckCircle className="w-3 h-3" />}>Active</EnhancedBadge>}
          >
            <EnhancedCardTitle>Interactive Card</EnhancedCardTitle>
            <p className="text-sm text-muted-foreground">
              Hover for interactive feedback
            </p>
          </EnhancedCardHeader>
          <EnhancedCardContent>
            <div className="space-y-4">
              <p className="text-sm">
                This card responds to hover with smooth animations and visual feedback.
              </p>
              <div className="flex space-x-2">
                <EnhancedButton variant="gradient-primary" size="sm">
                  Primary
                </EnhancedButton>
                <EnhancedButton variant="outline" size="sm">
                  Outline
                </EnhancedButton>
              </div>
            </div>
          </EnhancedCardContent>
        </EnhancedCard>

        {/* Elevated Card */}
        <EnhancedCard variant="elevated" hover="scale" padding="lg">
          <EnhancedCardHeader icon={<AlertTriangle className="w-5 h-5" />}>
            <EnhancedCardTitle>Elevated Card</EnhancedCardTitle>
            <p className="text-sm text-muted-foreground">
              Enhanced shadow and depth
            </p>
          </EnhancedCardHeader>
          <EnhancedCardContent>
            <div className="space-y-4">
              <p className="text-sm">
                This card features enhanced elevation with deeper shadows and scale effects.
              </p>
              <EnhancedButton 
                variant="gradient-warning" 
                icon={<Download className="w-4 h-4" />}
                rightIcon={<Plus className="w-4 h-4" />}
              >
                Download
              </EnhancedButton>
            </div>
          </EnhancedCardContent>
        </EnhancedCard>
      </div>

      {/* Buttons Showcase */}
      <EnhancedCard variant="default" padding="lg">
        <EnhancedCardHeader>
          <EnhancedCardTitle>Button Variations</EnhancedCardTitle>
          <p className="text-sm text-muted-foreground">
            Various button styles with enhanced interactions
          </p>
        </EnhancedCardHeader>
        <EnhancedCardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="space-y-3">
              <h4 className="font-medium text-sm">Gradient Buttons</h4>
              <div className="space-y-2">
                <EnhancedButton variant="gradient-primary" className="w-full">
                  Primary Gradient
                </EnhancedButton>
                <EnhancedButton variant="gradient-success" className="w-full">
                  Success Gradient
                </EnhancedButton>
                <EnhancedButton variant="gradient-warning" className="w-full">
                  Warning Gradient
                </EnhancedButton>
              </div>
            </div>
            
            <div className="space-y-3">
              <h4 className="font-medium text-sm">Loading States</h4>
              <div className="space-y-2">
                <EnhancedButton loading loadingText="Loading..." className="w-full">
                  Loading Button
                </EnhancedButton>
                <EnhancedButton variant="outline" loading className="w-full">
                  Processing
                </EnhancedButton>
              </div>
            </div>

            <div className="space-y-3">
              <h4 className="font-medium text-sm">With Icons</h4>
              <div className="space-y-2">
                <EnhancedButton icon={<Plus className="w-4 h-4" />} className="w-full">
                  Add Item
                </EnhancedButton>
                <EnhancedButton 
                  variant="outline" 
                  rightIcon={<Download className="w-4 h-4" />}
                  className="w-full"
                >
                  Download
                </EnhancedButton>
              </div>
            </div>

            <div className="space-y-3">
              <h4 className="font-medium text-sm">Glass Effect</h4>
              <div className="space-y-2">
                <EnhancedButton variant="glass" className="w-full">
                  Glass Button
                </EnhancedButton>
                <EnhancedButton variant="glass" icon={<Star className="w-4 h-4" />} className="w-full">
                  With Icon
                </EnhancedButton>
              </div>
            </div>
          </div>
        </EnhancedCardContent>
      </EnhancedCard>

      {/* Badges Showcase */}
      <EnhancedCard variant="default" padding="lg">
        <EnhancedCardHeader>
          <EnhancedCardTitle>Badge Variations</EnhancedCardTitle>
          <p className="text-sm text-muted-foreground">
            Status badges with semantic colors and effects
          </p>
        </EnhancedCardHeader>
        <EnhancedCardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="space-y-3">
              <h4 className="font-medium text-sm">Status Badges</h4>
              <div className="flex flex-wrap gap-2">
                <EnhancedBadge variant="success" icon={<CheckCircle className="w-3 h-3" />}>
                  Completed
                </EnhancedBadge>
                <EnhancedBadge variant="warning" icon={<Clock className="w-3 h-3" />}>
                  Pending
                </EnhancedBadge>
                <EnhancedBadge variant="info" icon={<FileText className="w-3 h-3" />}>
                  In Progress
                </EnhancedBadge>
                <EnhancedBadge variant="destructive" icon={<AlertTriangle className="w-3 h-3" />}>
                  Error
                </EnhancedBadge>
              </div>
            </div>

            <div className="space-y-3">
              <h4 className="font-medium text-sm">Sizes</h4>
              <div className="flex flex-wrap items-center gap-2">
                <EnhancedBadge size="sm" variant="info">Small</EnhancedBadge>
                <EnhancedBadge size="md" variant="info">Medium</EnhancedBadge>
                <EnhancedBadge size="lg" variant="info">Large</EnhancedBadge>
              </div>
            </div>

            <div className="space-y-3">
              <h4 className="font-medium text-sm">Effects</h4>
              <div className="flex flex-wrap gap-2">
                <EnhancedBadge variant="gradient" glow>
                  Gradient Glow
                </EnhancedBadge>
                <EnhancedBadge variant="success" pulse>
                  Pulsing
                </EnhancedBadge>
              </div>
            </div>
          </div>
        </EnhancedCardContent>
      </EnhancedCard>
    </div>
  );
};

export default EnhancedUIDemo;
