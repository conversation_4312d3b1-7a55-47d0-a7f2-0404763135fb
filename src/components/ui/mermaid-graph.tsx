import React, { useEffect, useRef, useState } from 'react';
import mermaid from 'mermaid';
import { ZoomIn, ZoomOut, RefreshCw, Download, Maximize } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface MermaidGraphProps {
  definition: string;
}

const MermaidGraph: React.FC<MermaidGraphProps> = ({ definition }) => {
  const diagramRef = useRef<HTMLDivElement>(null);
  const [zoomLevel, setZoomLevel] = useState(100); // Percentage
  const [renderedSvg, setRenderedSvg] = useState<string | null>(null);

  const renderMermaid = () => {
    if (diagramRef.current) {
      diagramRef.current.innerHTML = ''; 
      setRenderedSvg(null); // Clear previous SVG

      try {
        mermaid.render('mermaid-svg', definition)
          .then(({ svg }) => {
            if (diagramRef.current) {
              setRenderedSvg(svg);
            }
          })
          .catch(error => {
            console.error('Mermaid rendering error:', error);
            if (diagramRef.current) {
              diagramRef.current.innerHTML = `<p class="text-red-500">Error rendering graph: ${error.message}</p>`;
            }
          });
      } catch (e: any) {
        console.error('Mermaid parsing error:', e);
        if (diagramRef.current) {
          diagramRef.current.innerHTML = `<p class="text-red-500">Error parsing graph definition: ${e.message}</p>`;
        }
      }
    }
  };

  useEffect(() => {
    renderMermaid();
  }, [definition]);

  useEffect(() => {
    if (diagramRef.current && renderedSvg) {
      diagramRef.current.innerHTML = renderedSvg;
      const svgElement = diagramRef.current.querySelector('svg');
      if (svgElement) {
        svgElement.style.transform = `scale(${zoomLevel / 100})`;
        svgElement.style.transformOrigin = 'center center';
      }
    }
  }, [zoomLevel, renderedSvg]);

  const handleZoomIn = () => {
    setZoomLevel(prev => Math.min(prev + 10, 200));
  };

  const handleZoomOut = () => {
    setZoomLevel(prev => Math.max(prev - 10, 50));
  };

  const handleRefresh = () => {
    setZoomLevel(100); // Reset zoom on refresh
    renderMermaid();
  };

  return (
    <div className="mermaid-container w-full h-full flex flex-col items-center">
      {/* Top Bar Options */}
      <div className="flex items-center justify-end w-full p-2 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 rounded-t-lg">
        <Button variant="ghost" size="sm" onClick={handleZoomOut} title="Zoom Out">
          <ZoomOut className="h-4 w-4" />
        </Button>
        <span className="text-sm font-medium mx-2">{zoomLevel}%</span>
        <Button variant="ghost" size="sm" onClick={handleZoomIn} title="Zoom In">
          <ZoomIn className="h-4 w-4" />
        </Button>
        <Button variant="ghost" size="sm" onClick={handleRefresh} title="Refresh">
          <RefreshCw className="h-4 w-4" />
        </Button>
        <Button variant="ghost" size="sm" disabled title="Download (Coming Soon)">
          <Download className="h-4 w-4" />
        </Button>
        <Button variant="ghost" size="sm" disabled title="Expand (Coming Soon)">
          <Maximize className="h-4 w-4" />
        </Button>
      </div>

      <div 
        ref={diagramRef} 
        className="mermaid flex justify-center items-center min-h-[200px] bg-white dark:bg-gray-900 rounded-b-lg p-4 shadow-inner overflow-auto w-full"
        style={{ flexGrow: 1 }} // Allow the diagram area to grow
      />
    </div>
  );
};

export default MermaidGraph;
