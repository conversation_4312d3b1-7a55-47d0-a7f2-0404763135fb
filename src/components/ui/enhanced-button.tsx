import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { Loader2 } from 'lucide-react';

interface EnhancedButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link' | 'gradient-primary' | 'gradient-success' | 'gradient-warning' | 'glass';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  loading?: boolean;
  loadingText?: string;
  icon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  children: React.ReactNode;
}

const EnhancedButton = React.forwardRef<HTMLButtonElement, EnhancedButtonProps>(
  ({ 
    className, 
    variant = 'default', 
    size = 'default', 
    loading = false,
    loadingText,
    icon,
    rightIcon,
    disabled,
    children,
    ...props 
  }, ref) => {
    const getVariantClasses = () => {
      switch (variant) {
        case 'gradient-primary':
          return 'gradient-primary text-primary-foreground shadow-lg hover:shadow-xl transform hover:scale-[1.02] active:scale-[0.98] border-0';
        case 'gradient-success':
          return 'gradient-success text-success-foreground shadow-lg hover:shadow-xl transform hover:scale-[1.02] active:scale-[0.98] border-0';
        case 'gradient-warning':
          return 'gradient-warning text-warning-foreground shadow-lg hover:shadow-xl transform hover:scale-[1.02] active:scale-[0.98] border-0';
        case 'glass':
          return 'glass-card hover:bg-white/[0.08] text-foreground border-white/10 backdrop-blur-xl';
        default:
          return '';
      }
    };

    const baseClasses = cn(
      'focus-ring transition-all duration-200 font-medium',
      getVariantClasses()
    );

    const getBaseVariant = () => {
      if (variant.startsWith('gradient') || variant === 'glass') {
        return 'default';
      }
      return variant as 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';
    };

    return (
      <Button
        className={cn(baseClasses, className)}
        variant={getBaseVariant()}
        size={size}
        disabled={disabled || loading}
        ref={ref}
        {...props}
      >
        <div className="flex items-center justify-center space-x-2">
          {loading ? (
            <>
              <Loader2 className="w-4 h-4 animate-spin" />
              {loadingText && <span>{loadingText}</span>}
            </>
          ) : (
            <>
              {icon && <span className="flex-shrink-0">{icon}</span>}
              <span>{children}</span>
              {rightIcon && <span className="flex-shrink-0">{rightIcon}</span>}
            </>
          )}
        </div>
      </Button>
    );
  }
);

EnhancedButton.displayName = 'EnhancedButton';

export { EnhancedButton };
