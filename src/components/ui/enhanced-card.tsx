import React from 'react';
import { <PERSON>, <PERSON>Header, CardTitle, CardContent, CardDescription, CardFooter } from '@/components/ui/card';
import { cn } from '@/lib/utils';

interface EnhancedCardProps {
  children: React.ReactNode;
  className?: string;
  variant?: 'default' | 'glass' | 'interactive' | 'elevated';
  hover?: 'none' | 'lift' | 'glow' | 'scale';
  padding?: 'none' | 'sm' | 'md' | 'lg' | 'xl';
}

interface EnhancedCardHeaderProps {
  children: React.ReactNode;
  className?: string;
  icon?: React.ReactNode;
  badge?: React.ReactNode;
}

interface EnhancedCardContentProps {
  children: React.ReactNode;
  className?: string;
}

interface EnhancedCardTitleProps {
  children: React.ReactNode;
  className?: string;
  gradient?: boolean;
}

const EnhancedCard = React.forwardRef<HTMLDivElement, EnhancedCardProps>(
  ({ children, className, variant = 'default', hover = 'none', padding = 'md', ...props }, ref) => {
    const getVariantClasses = () => {
      switch (variant) {
        case 'glass':
          return 'glass-card border-white/10';
        case 'interactive':
          return 'bg-card border border-border/40 hover:border-primary/20 cursor-pointer';
        case 'elevated':
          return 'bg-card border border-border/20 shadow-lg';
        default:
          return 'bg-card border border-border/40';
      }
    };

    const getHoverClasses = () => {
      switch (hover) {
        case 'lift':
          return 'hover:-translate-y-2 hover:shadow-xl';
        case 'glow':
          return 'hover:shadow-2xl hover:shadow-primary/10';
        case 'scale':
          return 'hover:scale-[1.02]';
        default:
          return '';
      }
    };

    const getPaddingClasses = () => {
      switch (padding) {
        case 'none':
          return 'p-0';
        case 'sm':
          return 'p-3';
        case 'md':
          return 'p-4';
        case 'lg':
          return 'p-6';
        case 'xl':
          return 'p-8';
        default:
          return 'p-4';
      }
    };

    return (
      <Card
        ref={ref}
        className={cn(
          'transition-all duration-300 rounded-xl',
          getVariantClasses(),
          getHoverClasses(),
          getPaddingClasses(),
          className
        )}
        {...props}
      >
        {children}
      </Card>
    );
  }
);

const EnhancedCardHeader = React.forwardRef<HTMLDivElement, EnhancedCardHeaderProps>(
  ({ children, className, icon, badge, ...props }, ref) => {
    return (
      <CardHeader ref={ref} className={cn('pb-3', className)} {...props}>
        <div className="flex items-start justify-between">
          <div className="flex items-center space-x-3">
            {icon && (
              <div className="p-2 rounded-lg bg-primary/10 text-primary">
                {icon}
              </div>
            )}
            <div className="flex-1">{children}</div>
          </div>
          {badge && <div>{badge}</div>}
        </div>
      </CardHeader>
    );
  }
);

const EnhancedCardTitle = React.forwardRef<HTMLParagraphElement, EnhancedCardTitleProps>(
  ({ children, className, gradient = false, ...props }, ref) => {
    return (
      <CardTitle
        ref={ref}
        className={cn(
          'text-lg font-semibold',
          gradient && 'gradient-text',
          className
        )}
        {...props}
      >
        {children}
      </CardTitle>
    );
  }
);

const EnhancedCardContent = React.forwardRef<HTMLDivElement, EnhancedCardContentProps>(
  ({ children, className, ...props }, ref) => {
    return (
      <CardContent ref={ref} className={cn('pt-0', className)} {...props}>
        {children}
      </CardContent>
    );
  }
);

EnhancedCard.displayName = 'EnhancedCard';
EnhancedCardHeader.displayName = 'EnhancedCardHeader';
EnhancedCardTitle.displayName = 'EnhancedCardTitle';
EnhancedCardContent.displayName = 'EnhancedCardContent';

export { 
  EnhancedCard, 
  EnhancedCardHeader, 
  EnhancedCardTitle, 
  EnhancedCardContent,
  CardDescription,
  CardFooter
};
