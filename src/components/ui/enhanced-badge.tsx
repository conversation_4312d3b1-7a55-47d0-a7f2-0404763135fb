import React from 'react';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';

interface EnhancedBadgeProps {
  children: React.ReactNode;
  className?: string;
  variant?: 'default' | 'secondary' | 'destructive' | 'outline' | 'success' | 'warning' | 'info' | 'gradient';
  size?: 'sm' | 'md' | 'lg';
  icon?: React.ReactNode;
  pulse?: boolean;
  glow?: boolean;
}

const EnhancedBadge: React.FC<EnhancedBadgeProps> = ({ 
  children, 
  className, 
  variant = 'default', 
  size = 'md',
  icon,
  pulse = false,
  glow = false,
  ...props 
}) => {
    const getVariantClasses = () => {
      switch (variant) {
        case 'success':
          return 'status-success';
        case 'warning':
          return 'status-warning';
        case 'info':
          return 'status-info';
        case 'destructive':
          return 'status-error';
        case 'gradient':
          return 'gradient-primary text-primary-foreground border-0 shadow-md';
        default:
          return '';
      }
    };

    const getSizeClasses = () => {
      switch (size) {
        case 'sm':
          return 'text-xs px-2 py-0.5 h-5';
        case 'lg':
          return 'text-sm px-3 py-1 h-7';
        default:
          return 'text-xs px-2.5 py-0.5 h-6';
      }
    };

    const getEffectClasses = () => {
      let classes = '';
      if (pulse) classes += ' animate-pulse-subtle';
      if (glow) classes += ' shadow-lg shadow-current/25';
      return classes;
    };

    const baseClasses = cn(
      'status-badge inline-flex items-center justify-center font-medium transition-all duration-200',
      getSizeClasses(),
      getVariantClasses(),
      getEffectClasses()
    );

    const getBaseVariant = () => {
      if (['success', 'warning', 'info', 'gradient'].includes(variant)) {
        return 'default';
      }
      return variant as 'default' | 'secondary' | 'destructive' | 'outline';
    };

    return (
      <Badge
        className={cn(baseClasses, className)}
        variant={getBaseVariant()}
        {...props}
      >
        <div className="flex items-center space-x-1">
          {icon && (
            <span className="flex-shrink-0">
              {React.cloneElement(icon as React.ReactElement, { 
                className: cn('w-3 h-3', (icon as React.ReactElement).props?.className) 
              })}
            </span>
          )}
          <span>{children}</span>
        </div>
      </Badge>
    );
};

EnhancedBadge.displayName = 'EnhancedBadge';

export { EnhancedBadge };
