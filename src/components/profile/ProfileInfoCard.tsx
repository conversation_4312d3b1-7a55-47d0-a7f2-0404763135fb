
import React from 'react';
import { Building } from 'lucide-react';
import { User } from '@supabase/supabase-js';
import { 
  Card, 
  CardHeader, 
  CardTitle, 
  CardDescription 
} from '@/components/ui/card';
import UserProfileAvatar from './UserProfileAvatar';

interface ProfileInfoCardProps {
  user: User | null;
  profile: any | null;
}

const ProfileInfoCard: React.FC<ProfileInfoCardProps> = ({ user, profile }) => {
  return (
    <Card className="shadow-md">
      <CardHeader className="pb-4">
        <div className="flex items-center gap-4">
          <UserProfileAvatar user={user} profile={profile} size="lg" />
          <div>
            <CardTitle>{profile?.full_name || 'Profil utilisateur'}</CardTitle>
            <CardDescription className="mt-1">{user?.email}</CardDescription>
            {profile?.company && (
              <p className="text-sm mt-1 flex items-center gap-1 text-muted-foreground">
                <Building className="h-3.5 w-3.5" />
                <span>{profile.company}</span>
              </p>
            )}
          </div>
        </div>
      </CardHeader>
    </Card>
  );
};

export default ProfileInfoCard;
