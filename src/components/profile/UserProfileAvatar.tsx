
import React from 'react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { User } from '@supabase/supabase-js';

interface UserProfileAvatarProps {
  user: User | null;
  profile: any | null;
  size?: 'sm' | 'md' | 'lg';
}

const UserProfileAvatar: React.FC<UserProfileAvatarProps> = ({ 
  user, 
  profile, 
  size = 'md' 
}) => {
  // Get initials for avatar fallback
  const initials = profile?.full_name
    ? profile.full_name
        .split(' ')
        .map((n: string) => n[0])
        .join('')
        .toUpperCase()
        .substring(0, 2)
    : user?.email?.substring(0, 2).toUpperCase() || 'U';

  const sizeClasses = {
    sm: 'h-8 w-8',
    md: 'h-12 w-12',
    lg: 'h-20 w-20'
  };

  const initialsClasses = {
    sm: 'text-xs',
    md: 'text-sm',
    lg: 'text-xl'
  };
  
  return (
    <Avatar className={`${sizeClasses[size]} ${size === 'lg' ? 'border-4' : 'ring-2'} ${size === 'lg' ? 'border-primary/20' : 'ring-primary/20'}`}>
      <AvatarImage src={profile?.avatar_url || ''} alt={profile?.full_name || user?.email || ''} />
      <AvatarFallback className={`bg-gradient-to-br from-primary/80 to-primary text-primary-foreground font-medium ${initialsClasses[size]}`}>
        {initials}
      </AvatarFallback>
    </Avatar>
  );
};

export default UserProfileAvatar;
