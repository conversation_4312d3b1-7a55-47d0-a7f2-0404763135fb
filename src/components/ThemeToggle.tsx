
import { But<PERSON> } from '@/components/ui/button';
import { useTheme } from '@/components/ThemeProvider';
import { Moon, Sun } from 'lucide-react';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

export function ThemeToggle() {
  const { theme, toggleTheme } = useTheme();

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant="ghost"
            size="icon"
            onClick={toggleTheme}
            className="w-8 h-8 rounded-md transition-colors"
            aria-label="Basculer le thème"
          >
            {theme === 'dark' ? (
              <Sun className="h-[1.2rem] w-[1.2rem] text-amber-400 transition-all" />
            ) : (
              <Moon className="h-[1.2rem] w-[1.2rem] text-[#1a4734] transition-all" />
            )}
          </Button>
        </TooltipTrigger>
        <TooltipContent side="bottom">
          <p>{theme === 'dark' ? 'Passer en mode clair' : 'Passer en mode sombre'}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}
