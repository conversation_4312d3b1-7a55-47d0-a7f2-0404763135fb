
import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent } from '@/components/ui/card';
import { TrendingUp, TrendingDown, Minus } from 'lucide-react';

interface StatusCardProps {
  title: string;
  value: number;
  gradientClass?: string;
  trend?: 'up' | 'down' | 'neutral';
  trendValue?: string;
  icon?: React.ReactNode;
  variant?: 'default' | 'glass' | 'gradient';
}

const StatusCard: React.FC<StatusCardProps> = ({ 
  title, 
  value, 
  gradientClass, 
  trend,
  trendValue,
  icon,
  variant = 'default'
}) => {
  const getTrendIcon = () => {
    switch (trend) {
      case 'up':
        return <TrendingUp className="w-4 h-4 text-success" />;
      case 'down':
        return <TrendingDown className="w-4 h-4 text-destructive" />;
      case 'neutral':
        return <Minus className="w-4 h-4 text-muted-foreground" />;
      default:
        return null;
    }
  };

  const getTrendColor = () => {
    switch (trend) {
      case 'up':
        return 'text-success';
      case 'down':
        return 'text-destructive';
      default:
        return 'text-muted-foreground';
    }
  };

  const getCardClassName = () => {
    switch (variant) {
      case 'glass':
        return 'glass-card p-6 hover:bg-white/[0.05] transition-all duration-300';
      case 'gradient':
        return `stat-card bg-gradient-to-br ${gradientClass} text-white card-shadow`;
      default:
        return 'stat-card bg-card border border-border/40 card-shadow hover:border-primary/20';
    }
  };

  return (
    <Card className={getCardClassName()}>
      <CardHeader className="pb-3 pt-4 px-6">
        <div className="flex items-center justify-between">
          <CardTitle className={`stat-title font-medium ${
            variant === 'gradient' ? 'text-white/90' : 'text-muted-foreground'
          }`}>
            {title}
          </CardTitle>
          {icon && (
            <div className={`p-2 rounded-lg ${
              variant === 'gradient' 
                ? 'bg-white/10' 
                : 'bg-primary/10 text-primary'
            }`}>
              {icon}
            </div>
          )}
        </div>
      </CardHeader>
      <CardContent className="px-6 pb-4 pt-0">
        <div className="flex items-end justify-between">
          <div className={`stat-value font-bold ${
            variant === 'gradient' ? 'text-white' : 'text-foreground'
          }`}>
            {value.toLocaleString()}
          </div>
          {trend && trendValue && (
            <div className="flex items-center space-x-1">
              {getTrendIcon()}
              <span className={`text-sm font-medium ${getTrendColor()}`}>
                {trendValue}
              </span>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default StatusCard;
