
import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { Plus } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';

const DashboardHeader: React.FC = () => {
  return (
    <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
      <div>
        <h1 className="text-3xl font-bold">Tableau de bord</h1>
        <p className="text-muted-foreground">Bienvenue sur votre portail Notaflow</p>
      </div>
      
      <div className="flex items-center gap-3">
        <Button asChild variant="default" size="sm" className="flex items-center gap-1.5">
          <Link to="/new-request">
            <Plus className="h-4 w-4" />
            <span>Nouvelle demande</span>
          </Link>
        </Button>
      </div>
    </div>
  );
};

export default DashboardHeader;
