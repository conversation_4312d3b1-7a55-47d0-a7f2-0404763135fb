
import React from 'react';
import StatusCard from './StatusCard';
import { NotaryRequest } from '@/lib/data';

interface StatusOverviewProps {
  requests: NotaryRequest[];
}

const StatusOverview: React.FC<StatusOverviewProps> = ({ requests }) => {
  // Stats calculations
  const totalRequests = requests.length;
  
  // Count requests by status - normalizing status values
  const statusCounts = requests.reduce((acc, request) => {
    // Normalize the status to handle case variations and aliases
    let normalizedStatus = request.status?.toLowerCase() || 'unknown';
    
    // Map to standard status keys
    let countKey;
    if (normalizedStatus.includes('pending')) {
      countKey = 'pending';
    } else if (normalizedStatus.includes('inprogress') || normalizedStatus === 'in progress') {
      countKey = 'in progress';
    } else if (normalizedStatus.includes('phase 1') || normalizedStatus === 'processing_phase_1') {
      countKey = 'phase 1';
    } else if (normalizedStatus.includes('phase 2') || normalizedStatus === 'processing_phase_2') {
      countKey = 'phase 2';
    } else if (normalizedStatus.includes('phase 3') || normalizedStatus === 'processing_phase_3') {
      countKey = 'phase 3';
    } else if (normalizedStatus.includes('completed') || normalizedStatus.includes('approved')) {
      countKey = 'completed';
    } else {
      countKey = 'other';
    }
    
    acc[countKey] = (acc[countKey] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  // Translate status for display
  const translateStatus = (status: string): string => {
    switch (status?.toLowerCase()) {
      case 'pending':
        return 'En attente';
      case 'inprogress':
      case 'in progress':
        return 'En cours';
      case 'processing_phase_1':
      case 'phase 1':
        return 'Phase 1';
      case 'processing_phase_2':
      case 'phase 2':
        return 'Phase 2';
      case 'processing_phase_3':
      case 'phase 3':
        return 'Phase 3';
      case 'completed':
      case 'approved':
        return 'Complété';
      default:
        return status || 'Inconnu';
    }
  };
  
  // Order of statuses for display
  const statusOrder = ['pending', 'in progress', 'phase 1', 'phase 2', 'phase 3', 'completed'];

  // Theme-appropriate gradient colors for status cards
  const statusGradients = {
    'total': 'from-[#243949] to-[#517fa4]', // Darker blue gradient for total requests
    'pending': 'from-[#a08f62] to-[#c7b686]', // Warm gold gradient for pending
    'in progress': 'from-[#6a8583] to-[#8ea6a3]', // Muted sage gradient for in progress
    'phase 1': 'from-[#8b7e6d] to-[#aea193]', // Warm taupe gradient for phase 1
    'phase 2': 'from-[#7d8471] to-[#9ca794]', // Olive gradient for phase 2
    'phase 3': 'from-[#6b7f79] to-[#8a9e99]', // Eucalyptus gradient for phase 3
    'completed': 'from-[#1f5e42] to-[#367759]'  // Forest green gradient for completed
  };

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
      {/* Total Requests Card */}
      <StatusCard 
        title="Nombre total de demandes" 
        value={totalRequests} 
        gradientClass={statusGradients['total']} 
      />
      
      {/* Status Count Cards - Display in specific order with matching style */}
      {statusOrder.map(status => {
        const count = statusCounts[status] || 0;
        if (count === 0 && status !== 'completed' && status !== 'pending') return null;
        
        return (
          <StatusCard
            key={status}
            title={translateStatus(status)}
            value={count}
            gradientClass={statusGradients[status]}
          />
        );
      })}
    </div>
  );
};

export default StatusOverview;
