
import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { NotaryRequest } from '@/lib/data';
import RequestCard from '@/components/RequestCard';
import { Search, Plus, FileText, Loader2 } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';

interface RequestListProps {
  requests: NotaryRequest[];
  isLoading: boolean;
}

const RequestList: React.FC<RequestListProps> = ({ requests, isLoading }) => {
  const [searchTerm, setSearchTerm] = useState<string>('');

  const filteredRequests = requests.filter(request => {
    return searchTerm === '' || 
      request.seller_name?.toLowerCase().includes(searchTerm.toLowerCase()) || 
      request.property_address?.toLowerCase().includes(searchTerm.toLowerCase());
  });

  return (
    <div className="p-6 bg-card rounded-xl border border-border/40 shadow-sm">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4 mb-6">
        <h2 className="text-xl font-semibold flex items-center">
          <FileText className="h-5 w-5 mr-2 text-primary" />
          Mes demandes notariales
        </h2>
        
        <div className="flex flex-col sm:flex-row gap-3">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input placeholder="Rechercher..." className="pl-10 rounded-full" value={searchTerm} onChange={e => setSearchTerm(e.target.value)} />
          </div>
        </div>
      </div>
      
      {isLoading ? (
        <div className="flex justify-center items-center py-16">
          <Loader2 className="h-10 w-10 animate-spin text-primary" />
        </div>
      ) : filteredRequests.length === 0 ? (
        <div className="flex flex-col items-center justify-center py-16 space-y-4">
          <div className="rounded-full bg-muted p-6">
            <FileText className="h-10 w-10 text-muted-foreground" />
          </div>
          <p className="text-muted-foreground text-lg">Aucune demande trouvée</p>
          <Button asChild variant="outline">
            <Link to="/new-request">
              <Plus className="h-4 w-4 mr-2" />
              Créer une nouvelle demande
            </Link>
          </Button>
        </div>
      ) : (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
          {filteredRequests.map((request, index) => (
            <RequestCard key={request.id} request={request} index={index} />
          ))}
        </div>
      )}
    </div>
  );
};

export default RequestList;
