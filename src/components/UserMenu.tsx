
import React from 'react';
import { useAuth } from '@/context/AuthContext';
import { Link } from 'react-router-dom';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { User, Settings, LogOut, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';

const UserMenu = () => {
  const { user, profile, signOut, isLoading } = useAuth();
  
  const initials = profile?.full_name
    ? profile.full_name
        .split(' ')
        .map((n: string) => n[0])
        .join('')
        .toUpperCase()
        .substring(0, 2)
    : user?.email?.substring(0, 2).toUpperCase() || 'U';

  if (isLoading) {
    return (
      <Button variant="ghost" size="icon" disabled className="rounded-full w-8 h-8">
        <Loader2 className="h-4 w-4 animate-spin" />
      </Button>
    );
  }

  if (!user) {
    return (
      <Button asChild variant="ghost" size="sm">
        <Link to="/auth">
          <User className="h-4 w-4 mr-2" />
          Connexion
        </Link>
      </Button>
    );
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button 
          variant="ghost" 
          className="rounded-full p-0 w-8 h-8 hover:bg-accent/20" 
          aria-label="User menu"
        >
          <Avatar className="h-8 w-8 ring-2 ring-primary/20">
            <AvatarImage src={profile?.avatar_url} alt={profile?.full_name || user.email || ''} />
            <AvatarFallback className="bg-gradient-to-br from-primary/80 to-primary text-primary-foreground font-medium text-xs">{initials}</AvatarFallback>
          </Avatar>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent 
        align="end" 
        className="w-56 backdrop-blur-sm bg-card/95 border-border/30 shadow-xl rounded-xl"
      >
        <DropdownMenuLabel className="px-4 py-3">
          <div className="flex flex-col items-center space-y-2">
            <Avatar className="h-12 w-12 mb-1 ring-2 ring-primary/20">
              <AvatarImage src={profile?.avatar_url} alt={profile?.full_name || user.email || ''} />
              <AvatarFallback className="bg-gradient-to-br from-primary/80 to-primary text-primary-foreground text-sm font-medium">{initials}</AvatarFallback>
            </Avatar>
            <div className="text-center">
              <p className="text-sm font-medium leading-none">{profile?.full_name || 'Utilisateur'}</p>
              <p className="text-xs mt-1 text-muted-foreground">{user.email}</p>
            </div>
          </div>
        </DropdownMenuLabel>
        <DropdownMenuSeparator className="bg-border/20" />
        <DropdownMenuItem asChild className="cursor-pointer py-2 pl-4 focus:bg-accent/30 focus:text-accent-foreground">
          <Link to="/profile" className="flex items-center">
            <User className="mr-2 h-4 w-4" />
            <span>Profil</span>
          </Link>
        </DropdownMenuItem>
        {user.email?.endsWith('@notaireinc.com') && (
          <DropdownMenuItem asChild className="cursor-pointer py-2 pl-4 focus:bg-accent/30 focus:text-accent-foreground">
            <Link to="/webhook-configuration" className="flex items-center">
              <Settings className="mr-2 h-4 w-4" />
              <span>Configuration</span>
            </Link>
          </DropdownMenuItem>
        )}
        <DropdownMenuSeparator className="bg-border/20" />
        <DropdownMenuItem 
          onClick={() => signOut()}
          className="text-destructive focus:text-destructive cursor-pointer py-2 pl-4 focus:bg-destructive/10"
        >
          <LogOut className="mr-2 h-4 w-4" />
          <span>Déconnexion</span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default UserMenu;
