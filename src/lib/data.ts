
export interface NotaryRequest {
  id: string;
  created_at: string;
  seller_name: string;
  status: 'pending' | 'approved' | 'rejected' | 'inprogress' | 'processing_phase_1' | 'processing_phase_2' | 'processing_phase_3' | 'completed' | 'error' | 'finalizing' | string;
  property_address?: string;
  request_type?: string;
  priority?: 'low' | 'medium' | 'high';
}

export const notaryRequests: NotaryRequest[] = [
  {
    id: '001',
    created_at: '2023-12-01T09:30:00',
    seller_name: '<PERSON>',
    status: 'approved',
    property_address: '123 Rue Saint-Denis, Montréal, QC',
    request_type: 'Property Transfer',
    priority: 'medium'
  },
  {
    id: '002',
    created_at: '2023-12-05T14:20:00',
    seller_name: '<PERSON>',
    status: 'pending',
    property_address: '456 Boulevard René-Lévesque, Québec, QC',
    request_type: 'Mortgage Refinancing',
    priority: 'high'
  },
  {
    id: '003',
    created_at: '2023-12-10T11:15:00',
    seller_name: '<PERSON>',
    status: 'inprogress',
    property_address: '789 Avenue du Mont-Royal, Montréal, QC',
    request_type: 'Title Search',
    priority: 'low'
  },
  {
    id: '004',
    created_at: '2023-12-15T16:45:00',
    seller_name: '<PERSON>',
    status: 'rejected',
    property_address: '321 Rue Wellington, Sherbrooke, QC',
    request_type: 'Property Transfer',
    priority: 'medium'
  },
  {
    id: '005',
    created_at: '2023-12-20T10:00:00',
    seller_name: 'Luc Bouchard',
    status: 'pending',
    property_address: '654 Rue de la Commune, Montréal, QC',
    request_type: 'Will Preparation',
    priority: 'high'
  },
  {
    id: '006',
    created_at: '2023-12-22T13:30:00',
    seller_name: 'Isabelle Fortin',
    status: 'inprogress',
    property_address: '987 Chemin Sainte-Foy, Québec, QC',
    request_type: 'Mortgage Refinancing',
    priority: 'medium'
  },
  {
    id: '007',
    created_at: '2023-12-27T09:15:00',
    seller_name: 'Michel Dufresne',
    status: 'approved',
    property_address: '246 Boulevard Saint-Laurent, Montréal, QC',
    request_type: 'Title Search',
    priority: 'low'
  },
  {
    id: '008',
    created_at: '2023-12-30T15:45:00',
    seller_name: 'Émilie Tremblay',
    status: 'pending',
    property_address: '135 Rue Dalhousie, Québec, QC',
    request_type: 'Property Transfer',
    priority: 'high'
  }
];
