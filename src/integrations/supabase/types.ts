export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      acte_types: {
        Row: {
          acte_type: string
          category: string
          edge_cases: string | null
          extraction_analysis: string | null
          id: number
        }
        Insert: {
          acte_type: string
          category: string
          edge_cases?: string | null
          extraction_analysis?: string | null
          id?: number
        }
        Update: {
          acte_type?: string
          category?: string
          edge_cases?: string | null
          extraction_analysis?: string | null
          id?: number
        }
        Relationships: []
      }
      actes: {
        Row: {
          acte_details: string | null
          acte_nature: string | null
          acte_notary_minute: string | null
          acte_parties: string | null
          acte_publication_date: string | null
          acte_publication_number: string | null
          acte_summary: string | null
          circonscription_fonciere: string | null
          created_at: string | null
          doc_id: string | null
          doc_number: number | null
          doc_url: string | null
          document_completed: boolean
          document_ready: boolean
          file_content: string | null
          id: string
          index_id: string | null
          is_radiated: boolean | null
          matrimonie: string | null
          other_details: Json | null
          radiation_number: string | null
          relevance_explanation: string | null
          relevance_rating: number | null
          request_id: string
          source_id: string | null
          source_type: string | null
          status: Database["public"]["Enums"]["acte_status_enum"]
          updated_at: string | null
          writting: string | null
        }
        Insert: {
          acte_details?: string | null
          acte_nature?: string | null
          acte_notary_minute?: string | null
          acte_parties?: string | null
          acte_publication_date?: string | null
          acte_publication_number?: string | null
          acte_summary?: string | null
          circonscription_fonciere?: string | null
          created_at?: string | null
          doc_id?: string | null
          doc_number?: number | null
          doc_url?: string | null
          document_completed?: boolean
          document_ready?: boolean
          file_content?: string | null
          id?: string
          index_id?: string | null
          is_radiated?: boolean | null
          matrimonie?: string | null
          other_details?: Json | null
          radiation_number?: string | null
          relevance_explanation?: string | null
          relevance_rating?: number | null
          request_id: string
          source_id?: string | null
          source_type?: string | null
          status?: Database["public"]["Enums"]["acte_status_enum"]
          updated_at?: string | null
          writting?: string | null
        }
        Update: {
          acte_details?: string | null
          acte_nature?: string | null
          acte_notary_minute?: string | null
          acte_parties?: string | null
          acte_publication_date?: string | null
          acte_publication_number?: string | null
          acte_summary?: string | null
          circonscription_fonciere?: string | null
          created_at?: string | null
          doc_id?: string | null
          doc_number?: number | null
          doc_url?: string | null
          document_completed?: boolean
          document_ready?: boolean
          file_content?: string | null
          id?: string
          index_id?: string | null
          is_radiated?: boolean | null
          matrimonie?: string | null
          other_details?: Json | null
          radiation_number?: string | null
          relevance_explanation?: string | null
          relevance_rating?: number | null
          request_id?: string
          source_id?: string | null
          source_type?: string | null
          status?: Database["public"]["Enums"]["acte_status_enum"]
          updated_at?: string | null
          writting?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "actes_index_id_fkey"
            columns: ["index_id"]
            isOneToOne: false
            referencedRelation: "index"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "actes_request_id_fkey"
            columns: ["request_id"]
            isOneToOne: false
            referencedRelation: "requests"
            referencedColumns: ["id"]
          },
        ]
      }
      actes_documents: {
        Row: {
          acte_publication_number: string
          acte_type: number | null
          alternative_names: Json | null
          created_at: string
          doc_summary: string | null
          document_id: string | null
          document_url: string | null
          file_content: string | null
          publication_date: string | null
        }
        Insert: {
          acte_publication_number: string
          acte_type?: number | null
          alternative_names?: Json | null
          created_at?: string
          doc_summary?: string | null
          document_id?: string | null
          document_url?: string | null
          file_content?: string | null
          publication_date?: string | null
        }
        Update: {
          acte_publication_number?: string
          acte_type?: number | null
          alternative_names?: Json | null
          created_at?: string
          doc_summary?: string | null
          document_id?: string | null
          document_url?: string | null
          file_content?: string | null
          publication_date?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "actes_documents_acte_type_fkey"
            columns: ["acte_type"]
            isOneToOne: false
            referencedRelation: "acte_types"
            referencedColumns: ["id"]
          },
        ]
      }
      chat_sessions: {
        Row: {
          created_at: string
          id: string
          metadata: Json | null
          updated_at: string
          user_id: string | null
        }
        Insert: {
          created_at?: string
          id?: string
          metadata?: Json | null
          updated_at?: string
          user_id?: string | null
        }
        Update: {
          created_at?: string
          id?: string
          metadata?: Json | null
          updated_at?: string
          user_id?: string | null
        }
        Relationships: []
      }
      dates: {
        Row: {
          date: string
          date_type: string | null
          id: number
          legal_document_id: number | null
          range_end: string | null
          range_start: string | null
        }
        Insert: {
          date: string
          date_type?: string | null
          id?: never
          legal_document_id?: number | null
          range_end?: string | null
          range_start?: string | null
        }
        Update: {
          date?: string
          date_type?: string | null
          id?: never
          legal_document_id?: number | null
          range_end?: string | null
          range_start?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "dates_legal_document_id_fkey"
            columns: ["legal_document_id"]
            isOneToOne: false
            referencedRelation: "legal_documents"
            referencedColumns: ["id"]
          },
        ]
      }
      error_log: {
        Row: {
          created_at: string
          error: string | null
          flow_name: string | null
          id: number
        }
        Insert: {
          created_at?: string
          error?: string | null
          flow_name?: string | null
          id?: number
        }
        Update: {
          created_at?: string
          error?: string | null
          flow_name?: string | null
          id?: number
        }
        Relationships: []
      }
      extraction_queue: {
        Row: {
          acte_id: string | null
          acte_type: Database["public"]["Enums"]["acte_type_enum"] | null
          cadastre: string | null
          circonscription_fonciere: string | null
          created_at: string
          designation_secondaire: string | null
          doc_id: string | null
          doc_url: string | null
          document_number: string
          document_source: Database["public"]["Enums"]["document_source_enum"]
          error_message: string | null
          file_content: string | null
          id: string
          index_id: string | null
          local_file_path: string | null
          logged: boolean
          processing_started_at: string | null
          request_id: string | null
          retry_count: number
          status: Database["public"]["Enums"]["extraction_status_enum"]
          updated_at: string
        }
        Insert: {
          acte_id?: string | null
          acte_type?: Database["public"]["Enums"]["acte_type_enum"] | null
          cadastre?: string | null
          circonscription_fonciere?: string | null
          created_at?: string
          designation_secondaire?: string | null
          doc_id?: string | null
          doc_url?: string | null
          document_number: string
          document_source: Database["public"]["Enums"]["document_source_enum"]
          error_message?: string | null
          file_content?: string | null
          id?: string
          index_id?: string | null
          local_file_path?: string | null
          logged?: boolean
          processing_started_at?: string | null
          request_id?: string | null
          retry_count?: number
          status?: Database["public"]["Enums"]["extraction_status_enum"]
          updated_at?: string
        }
        Update: {
          acte_id?: string | null
          acte_type?: Database["public"]["Enums"]["acte_type_enum"] | null
          cadastre?: string | null
          circonscription_fonciere?: string | null
          created_at?: string
          designation_secondaire?: string | null
          doc_id?: string | null
          doc_url?: string | null
          document_number?: string
          document_source?: Database["public"]["Enums"]["document_source_enum"]
          error_message?: string | null
          file_content?: string | null
          id?: string
          index_id?: string | null
          local_file_path?: string | null
          logged?: boolean
          processing_started_at?: string | null
          request_id?: string | null
          retry_count?: number
          status?: Database["public"]["Enums"]["extraction_status_enum"]
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "extraction_queue_acte_id_fkey"
            columns: ["acte_id"]
            isOneToOne: false
            referencedRelation: "actes"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "extraction_queue_index_id_fkey"
            columns: ["index_id"]
            isOneToOne: false
            referencedRelation: "index"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "extraction_queue_request_id_fkey"
            columns: ["request_id"]
            isOneToOne: false
            referencedRelation: "requests"
            referencedColumns: ["id"]
          },
        ]
      }
      index: {
        Row: {
          actes_completed: number
          cadastre: string | null
          circonscription: string | null
          created_at: string | null
          designation_secondaire: string | null
          doc_id: string | null
          doc_number: number | null
          doc_url: string | null
          document_acquisition_round: number
          document_ready: boolean
          file_content: string | null
          id: string
          index_initial: boolean
          index_summary: string | null
          is_completed: boolean
          lot_number: string | null
          phase_1_completed: boolean
          phase_1_status: Database["public"]["Enums"]["index_phase_status_enum"]
          phase_2_completed: boolean
          phase_2_status: Database["public"]["Enums"]["phase_2_status_enum"]
          phase_3_completed: boolean
          related_actes: number
          relevance_explanation: string | null
          relevance_rating: number | null
          request_id: string
          status: Database["public"]["Enums"]["index_status_enum"]
          updated_at: string | null
        }
        Insert: {
          actes_completed?: number
          cadastre?: string | null
          circonscription?: string | null
          created_at?: string | null
          designation_secondaire?: string | null
          doc_id?: string | null
          doc_number?: number | null
          doc_url?: string | null
          document_acquisition_round?: number
          document_ready?: boolean
          file_content?: string | null
          id?: string
          index_initial?: boolean
          index_summary?: string | null
          is_completed?: boolean
          lot_number?: string | null
          phase_1_completed?: boolean
          phase_1_status?: Database["public"]["Enums"]["index_phase_status_enum"]
          phase_2_completed?: boolean
          phase_2_status?: Database["public"]["Enums"]["phase_2_status_enum"]
          phase_3_completed?: boolean
          related_actes?: number
          relevance_explanation?: string | null
          relevance_rating?: number | null
          request_id: string
          status?: Database["public"]["Enums"]["index_status_enum"]
          updated_at?: string | null
        }
        Update: {
          actes_completed?: number
          cadastre?: string | null
          circonscription?: string | null
          created_at?: string | null
          designation_secondaire?: string | null
          doc_id?: string | null
          doc_number?: number | null
          doc_url?: string | null
          document_acquisition_round?: number
          document_ready?: boolean
          file_content?: string | null
          id?: string
          index_initial?: boolean
          index_summary?: string | null
          is_completed?: boolean
          lot_number?: string | null
          phase_1_completed?: boolean
          phase_1_status?: Database["public"]["Enums"]["index_phase_status_enum"]
          phase_2_completed?: boolean
          phase_2_status?: Database["public"]["Enums"]["phase_2_status_enum"]
          phase_3_completed?: boolean
          related_actes?: number
          relevance_explanation?: string | null
          relevance_rating?: number | null
          request_id?: string
          status?: Database["public"]["Enums"]["index_status_enum"]
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "index_request_id_fkey"
            columns: ["request_id"]
            isOneToOne: false
            referencedRelation: "requests"
            referencedColumns: ["id"]
          },
        ]
      }
      key_clauses_provisions: {
        Row: {
          clause_summary: string
          clause_title: string
          id: number
          legal_document_id: number | null
        }
        Insert: {
          clause_summary: string
          clause_title: string
          id?: never
          legal_document_id?: number | null
        }
        Update: {
          clause_summary?: string
          clause_title?: string
          id?: never
          legal_document_id?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "key_clauses_provisions_legal_document_id_fkey"
            columns: ["legal_document_id"]
            isOneToOne: false
            referencedRelation: "legal_documents"
            referencedColumns: ["id"]
          },
        ]
      }
      legal_documents: {
        Row: {
          acte_publication_number: string | null
          file_content: string | null
          id: number
          publication_date: string | null
          summary: string | null
        }
        Insert: {
          acte_publication_number?: string | null
          file_content?: string | null
          id?: never
          publication_date?: string | null
          summary?: string | null
        }
        Update: {
          acte_publication_number?: string | null
          file_content?: string | null
          id?: never
          publication_date?: string | null
          summary?: string | null
        }
        Relationships: []
      }
      messages: {
        Row: {
          chat_session_id: string
          content: string
          created_at: string
          id: string
          metadata: Json | null
          request_id: string | null
          sender: string
          user_id: string | null
        }
        Insert: {
          chat_session_id: string
          content: string
          created_at?: string
          id?: string
          metadata?: Json | null
          request_id?: string | null
          sender: string
          user_id?: string | null
        }
        Update: {
          chat_session_id?: string
          content?: string
          created_at?: string
          id?: string
          metadata?: Json | null
          request_id?: string | null
          sender?: string
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "messages_chat_session_id_fkey"
            columns: ["chat_session_id"]
            isOneToOne: false
            referencedRelation: "chat_sessions"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "messages_request_id_fkey"
            columns: ["request_id"]
            isOneToOne: false
            referencedRelation: "requests"
            referencedColumns: ["id"]
          },
        ]
      }
      notary_and_references: {
        Row: {
          id: number
          jurisdiction: string | null
          legal_document_id: number | null
          legal_references: string[] | null
          location: string | null
          notary_name: string | null
        }
        Insert: {
          id?: never
          jurisdiction?: string | null
          legal_document_id?: number | null
          legal_references?: string[] | null
          location?: string | null
          notary_name?: string | null
        }
        Update: {
          id?: never
          jurisdiction?: string | null
          legal_document_id?: number | null
          legal_references?: string[] | null
          location?: string | null
          notary_name?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "notary_and_references_legal_document_id_fkey"
            columns: ["legal_document_id"]
            isOneToOne: false
            referencedRelation: "legal_documents"
            referencedColumns: ["id"]
          },
        ]
      }
      notifications: {
        Row: {
          content: string
          created_at: string | null
          id: string
          is_read: boolean | null
          request_id: string | null
          title: string
          updated_at: string | null
          user_id: string
        }
        Insert: {
          content: string
          created_at?: string | null
          id?: string
          is_read?: boolean | null
          request_id?: string | null
          title: string
          updated_at?: string | null
          user_id: string
        }
        Update: {
          content?: string
          created_at?: string | null
          id?: string
          is_read?: boolean | null
          request_id?: string | null
          title?: string
          updated_at?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "notifications_request_id_fkey"
            columns: ["request_id"]
            isOneToOne: false
            referencedRelation: "requests"
            referencedColumns: ["id"]
          },
        ]
      }
      parties: {
        Row: {
          address: string | null
          authorized_representative: string | null
          id: number
          legal_document_id: number | null
          legal_form: string | null
          legal_identifiers: string | null
          name: string
          role: string | null
          type: string
        }
        Insert: {
          address?: string | null
          authorized_representative?: string | null
          id?: never
          legal_document_id?: number | null
          legal_form?: string | null
          legal_identifiers?: string | null
          name: string
          role?: string | null
          type: string
        }
        Update: {
          address?: string | null
          authorized_representative?: string | null
          id?: never
          legal_document_id?: number | null
          legal_form?: string | null
          legal_identifiers?: string | null
          name?: string
          role?: string | null
          type?: string
        }
        Relationships: [
          {
            foreignKeyName: "parties_legal_document_id_fkey"
            columns: ["legal_document_id"]
            isOneToOne: false
            referencedRelation: "legal_documents"
            referencedColumns: ["id"]
          },
        ]
      }
      profiles: {
        Row: {
          avatar_url: string | null
          company: string | null
          created_at: string
          File_instructions: string | null
          Folder_url: string | null
          full_name: string | null
          id: string
          is_approved: boolean
          template_id: string | null
          updated_at: string
        }
        Insert: {
          avatar_url?: string | null
          company?: string | null
          created_at?: string
          File_instructions?: string | null
          Folder_url?: string | null
          full_name?: string | null
          id: string
          is_approved?: boolean
          template_id?: string | null
          updated_at?: string
        }
        Update: {
          avatar_url?: string | null
          company?: string | null
          created_at?: string
          File_instructions?: string | null
          Folder_url?: string | null
          full_name?: string | null
          id?: string
          is_approved?: boolean
          template_id?: string | null
          updated_at?: string
        }
        Relationships: []
      }
      requests: {
        Row: {
          autres_considerations: string | null
          circonscription: string | null
          complete_summary: string[] | null
          completed_at: string | null
          created_at: string | null
          erreurs: string | null
          final_doc_id: string | null
          final_doc_link: string | null
          folder_id: string | null
          folder_link: string | null
          hypotheques_years: number | null
          id: string
          inclure_actes_radies: boolean | null
          index_completed: number
          long_term_memory_doc_id: string | null
          number_of_actes: number | null
          number_of_index: number | null
          regimes_matrimoniaux: string | null
          request_summary: string
          resume_etapes_recherche: string | null
          sales_years: number | null
          seller_address: string
          seller_name: string
          servitudes: string | null
          status: string
          updated_at: string | null
          user_id: string
        }
        Insert: {
          autres_considerations?: string | null
          circonscription?: string | null
          complete_summary?: string[] | null
          completed_at?: string | null
          created_at?: string | null
          erreurs?: string | null
          final_doc_id?: string | null
          final_doc_link?: string | null
          folder_id?: string | null
          folder_link?: string | null
          hypotheques_years?: number | null
          id?: string
          inclure_actes_radies?: boolean | null
          index_completed?: number
          long_term_memory_doc_id?: string | null
          number_of_actes?: number | null
          number_of_index?: number | null
          regimes_matrimoniaux?: string | null
          request_summary: string
          resume_etapes_recherche?: string | null
          sales_years?: number | null
          seller_address: string
          seller_name: string
          servitudes?: string | null
          status?: string
          updated_at?: string | null
          user_id: string
        }
        Update: {
          autres_considerations?: string | null
          circonscription?: string | null
          complete_summary?: string[] | null
          completed_at?: string | null
          created_at?: string | null
          erreurs?: string | null
          final_doc_id?: string | null
          final_doc_link?: string | null
          folder_id?: string | null
          folder_link?: string | null
          hypotheques_years?: number | null
          id?: string
          inclure_actes_radies?: boolean | null
          index_completed?: number
          long_term_memory_doc_id?: string | null
          number_of_actes?: number | null
          number_of_index?: number | null
          regimes_matrimoniaux?: string | null
          request_summary?: string
          resume_etapes_recherche?: string | null
          sales_years?: number | null
          seller_address?: string
          seller_name?: string
          servitudes?: string | null
          status?: string
          updated_at?: string | null
          user_id?: string
        }
        Relationships: []
      }
      signatures_attestations: {
        Row: {
          id: number
          legal_document_id: number | null
          signature_text: string | null
        }
        Insert: {
          id?: never
          legal_document_id?: number | null
          signature_text?: string | null
        }
        Update: {
          id?: never
          legal_document_id?: number | null
          signature_text?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "signatures_attestations_legal_document_id_fkey"
            columns: ["legal_document_id"]
            isOneToOne: false
            referencedRelation: "legal_documents"
            referencedColumns: ["id"]
          },
        ]
      }
      workflow_state: {
        Row: {
          in_progress: boolean | null
          workflow: string
        }
        Insert: {
          in_progress?: boolean | null
          workflow?: string
        }
        Update: {
          in_progress?: boolean | null
          workflow?: string
        }
        Relationships: []
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      bytea_to_text: {
        Args: { data: string }
        Returns: string
      }
      check_additional_actes: {
        Args: { p_index_id: string; p_acte_level: number }
        Returns: {
          acte_details: string | null
          acte_nature: string | null
          acte_notary_minute: string | null
          acte_parties: string | null
          acte_publication_date: string | null
          acte_publication_number: string | null
          acte_summary: string | null
          circonscription_fonciere: string | null
          created_at: string | null
          doc_id: string | null
          doc_number: number | null
          doc_url: string | null
          document_completed: boolean
          document_ready: boolean
          file_content: string | null
          id: string
          index_id: string | null
          is_radiated: boolean | null
          matrimonie: string | null
          other_details: Json | null
          radiation_number: string | null
          relevance_explanation: string | null
          relevance_rating: number | null
          request_id: string
          source_id: string | null
          source_type: string | null
          status: Database["public"]["Enums"]["acte_status_enum"]
          updated_at: string | null
          writting: string | null
        }[]
      }
      get_final_doc_creation_instructions: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      http: {
        Args: { request: Database["public"]["CompositeTypes"]["http_request"] }
        Returns: unknown
      }
      http_delete: {
        Args:
          | { uri: string }
          | { uri: string; content: string; content_type: string }
        Returns: unknown
      }
      http_get: {
        Args: { uri: string } | { uri: string; data: Json }
        Returns: unknown
      }
      http_head: {
        Args: { uri: string }
        Returns: unknown
      }
      http_header: {
        Args: { field: string; value: string }
        Returns: Database["public"]["CompositeTypes"]["http_header"]
      }
      http_list_curlopt: {
        Args: Record<PropertyKey, never>
        Returns: {
          curlopt: string
          value: string
        }[]
      }
      http_patch: {
        Args: { uri: string; content: string; content_type: string }
        Returns: unknown
      }
      http_post: {
        Args:
          | { uri: string; content: string; content_type: string }
          | { uri: string; data: Json }
        Returns: unknown
      }
      http_put: {
        Args: { uri: string; content: string; content_type: string }
        Returns: unknown
      }
      http_reset_curlopt: {
        Args: Record<PropertyKey, never>
        Returns: boolean
      }
      http_set_curlopt: {
        Args: { curlopt: string; value: string }
        Returns: boolean
      }
      manually_trigger_final_doc_creation: {
        Args: { p_request_id: string }
        Returns: string
      }
      manually_trigger_final_doc_webhook: {
        Args: { p_request_id: string }
        Returns: string
      }
      mark_queue_item_completed: {
        Args: { p_queue_id: string }
        Returns: undefined
      }
      mark_queue_item_error: {
        Args: { p_queue_id: string; p_error_message: string }
        Returns: undefined
      }
      queue_additional_actes: {
        Args: {
          p_request_id: string
          p_index_id: string
          p_acte_ids: string[]
          p_phase: number
        }
        Returns: undefined
      }
      queue_eligible_documents_for_phase: {
        Args: { p_request_id: string; p_phase: number }
        Returns: undefined
      }
      queue_index_actes: {
        Args: {
          p_request_id: string
          p_index_id: string
          p_phase: number
          p_index_level: number
        }
        Returns: undefined
      }
      queue_index_concordances: {
        Args: { p_request_id: string; p_source_index_id: string }
        Returns: undefined
      }
      queue_index_reanalysis: {
        Args: {
          p_request_id: string
          p_index_id: string
          p_index_level: number
          p_phase: number
        }
        Returns: undefined
      }
      ready_for_analysis: {
        Args: { doc_type: string; doc_id: string }
        Returns: boolean
      }
      text_to_bytea: {
        Args: { data: string }
        Returns: string
      }
      urlencode: {
        Args: { data: Json } | { string: string } | { string: string }
        Returns: string
      }
    }
    Enums: {
      acte_status_enum:
        | "Pending"
        | "Ready for Analysis"
        | "Analysis in Progress"
        | "Analysis Completed"
        | "Document not Available"
      acte_type_enum: "Acte" | "Avis d'adresse" | "Radiation" | "Acte divers"
      document_source_enum: "acte" | "index"
      extraction_status_enum:
        | "Pending"
        | "Processing"
        | "Downloaded"
        | "Available on Drive"
        | "Document not Found"
      index_phase_status_enum:
        | "Pending"
        | "Ready for Analysis"
        | "Analysis in Progress"
        | "Analysis Completed"
        | "Document not Available"
      index_status_enum:
        | "Phase 1"
        | "Waiting Phase 2"
        | "Phase 2"
        | "Phase 3"
        | "Completed"
      phase_2_status_enum:
        | "Pending"
        | "Actes Analysis in Progress"
        | "Analysis Completed"
      request_status_enum:
        | "Pending"
        | "In Progress"
        | "Phase 1"
        | "Phase 2"
        | "Phase 3"
        | "Completed"
    }
    CompositeTypes: {
      http_header: {
        field: string | null
        value: string | null
      }
      http_request: {
        method: unknown | null
        uri: string | null
        headers: Database["public"]["CompositeTypes"]["http_header"][] | null
        content_type: string | null
        content: string | null
      }
      http_response: {
        status: number | null
        content_type: string | null
        headers: Database["public"]["CompositeTypes"]["http_header"][] | null
        content: string | null
      }
    }
  }
}

type DefaultSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {
      acte_status_enum: [
        "Pending",
        "Ready for Analysis",
        "Analysis in Progress",
        "Analysis Completed",
        "Document not Available",
      ],
      acte_type_enum: ["Acte", "Avis d'adresse", "Radiation", "Acte divers"],
      document_source_enum: ["acte", "index"],
      extraction_status_enum: [
        "Pending",
        "Processing",
        "Downloaded",
        "Available on Drive",
        "Document not Found",
      ],
      index_phase_status_enum: [
        "Pending",
        "Ready for Analysis",
        "Analysis in Progress",
        "Analysis Completed",
        "Document not Available",
      ],
      index_status_enum: [
        "Phase 1",
        "Waiting Phase 2",
        "Phase 2",
        "Phase 3",
        "Completed",
      ],
      phase_2_status_enum: [
        "Pending",
        "Actes Analysis in Progress",
        "Analysis Completed",
      ],
      request_status_enum: [
        "Pending",
        "In Progress",
        "Phase 1",
        "Phase 2",
        "Phase 3",
        "Completed",
      ],
    },
  },
} as const
