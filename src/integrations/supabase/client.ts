// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://sqzqvxqcybghcgrpubsy.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNxenF2eHFjeWJnaGNncnB1YnN5Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDEwOTU1MTgsImV4cCI6MjA1NjY3MTUxOH0.CGPpl3fF15Z9NSbvCZXaawWph3r5LGloQjoXlGqKFds";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);