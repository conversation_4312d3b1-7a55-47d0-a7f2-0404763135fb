
import React from 'react';
import { useAuth } from '@/context/AuthContext';
import ProfileInfoCard from '@/components/profile/ProfileInfoCard';
import ProfileForm from '@/components/profile/ProfileForm';
import { UserCircle } from 'lucide-react';

const Profile = () => {
  const { user, profile, refreshProfile } = useAuth();

  return (
    <div className="container max-w-4xl py-10 px-4">
      <div className="flex items-center mb-6">
        <div className="h-12 w-12 rounded-lg bg-primary/10 flex items-center justify-center mr-3">
          <UserCircle className="h-7 w-7 text-primary" />
        </div>
        <div>
          <h1 className="text-2xl font-bold">Mon profil</h1>
          <p className="text-muted-foreground">Gérez vos informations personnelles et préférences</p>
        </div>
      </div>
      
      <div className="flex flex-col gap-6">
        <div className="p-6 bg-card rounded-xl border border-border/40 shadow-sm">
          <ProfileInfoCard user={user} profile={profile} />
        </div>
        
        <div className="p-6 bg-card rounded-xl border border-border/40 shadow-sm">
          <ProfileForm user={user} profile={profile} refreshProfile={refreshProfile} />
        </div>
      </div>
    </div>
  );
};

export default Profile;
