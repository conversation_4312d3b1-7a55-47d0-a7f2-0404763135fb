
import React, { useState } from 'react';
import { useN<PERSON><PERSON>, Link } from 'react-router-dom';
import { supabase } from '@/lib/supabase';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { MailCheck, Loader2, ArrowLeft } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { Alert, AlertDescription } from '@/components/ui/alert';

const ResetPassword = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const [email, setEmail] = useState('');
  const [loading, setLoading] = useState(false);
  const [resetSent, setResetSent] = useState(false);

  const handleResetPassword = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    try {
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}/auth?resetPassword=true`,
      });

      if (error) {
        console.error('Reset password error:', error);
        toast({
          title: 'Erreur',
          description: error.message || 'Une erreur est survenue lors de la réinitialisation du mot de passe.',
          variant: 'destructive'
        });
      } else {
        setResetSent(true);
        toast({
          title: 'Email envoyé',
          description: 'Veuillez vérifier votre boîte de réception pour les instructions de réinitialisation du mot de passe.'
        });
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="flex items-center justify-center min-h-screen bg-background p-4">
      <div className="w-full max-w-md">
        <div className="text-center mb-8">
          <Link to="/" className="inline-block">
            <div className="flex items-center justify-center mb-2">
              <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-primary via-blue-400 to-blue-600 flex items-center justify-center text-primary-foreground font-bold text-xl shadow-lg">
                N
              </div>
            </div>
            <h1 className="text-2xl font-bold tracking-tight gradient-text">Notaflow</h1>
          </Link>
          <p className="text-sm text-muted-foreground mt-2">
            Plateforme de gestion de recherches notariales au Québec
          </p>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Réinitialisation du mot de passe</CardTitle>
            <CardDescription>Saisissez votre adresse e-mail pour recevoir un lien de réinitialisation</CardDescription>
          </CardHeader>
          
          {resetSent ? (
            <>
              <CardContent className="space-y-4">
                <Alert>
                  <AlertDescription>
                    Un email de réinitialisation de mot de passe a été envoyé à {email}. Veuillez vérifier votre boîte de réception et suivre les instructions.
                  </AlertDescription>
                </Alert>
              </CardContent>
              <CardFooter className="flex justify-between">
                <Button variant="outline" onClick={() => navigate('/auth')}>
                  <ArrowLeft className="mr-2 h-4 w-4" />
                  Retour à la connexion
                </Button>
                <Button onClick={() => {
                  setResetSent(false);
                  setEmail('');
                }}>
                  Réessayer
                </Button>
              </CardFooter>
            </>
          ) : (
            <form onSubmit={handleResetPassword}>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="email">Adresse courriel</Label>
                  <div className="relative">
                    <Input 
                      id="email" 
                      type="email" 
                      placeholder="<EMAIL>" 
                      value={email} 
                      onChange={e => setEmail(e.target.value)} 
                      required 
                      className="pl-10" 
                    />
                    <MailCheck className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                  </div>
                </div>
              </CardContent>
              
              <CardFooter className="flex justify-between">
                <Button variant="outline" onClick={() => navigate('/auth')}>
                  <ArrowLeft className="mr-2 h-4 w-4" />
                  Retour à la connexion
                </Button>
                <Button type="submit" disabled={loading}>
                  {loading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Envoi en cours...
                    </>
                  ) : 'Envoyer le lien'}
                </Button>
              </CardFooter>
            </form>
          )}
        </Card>
      </div>
    </div>
  );
};

export default ResetPassword;
