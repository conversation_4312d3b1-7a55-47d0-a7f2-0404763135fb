
import React from 'react';
import TopNavbar from '@/components/TopNavbar';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Settings, Save, Send, RefreshCw, DatabaseIcon } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { useWebhook } from '@/hooks/use-webhook';
import RequestFormWebhook from '@/components/forms/RequestFormWebhook';

const WebhookConfiguration: React.FC = () => {
  const { toast } = useToast();
  const { 
    webhookUrl, 
    webhookMethod, 
    handleWebhookUrlChange, 
    handleWebhookMethodChange,
    statusWebhookUrl,
    handleStatusWebhookUrlChange,
    acteWebhookUrl,
    handleActeWebhookUrlChange
  } = useWebhook();

  const handleSave = () => {
    // The useWebhook hook already saves to localStorage on change
    // Just show a confirmation toast
    toast({
      title: 'Configuration sauvegardée',
      description: 'Les paramètres des webhooks ont été sauvegardés avec succès.',
    });
  };

  return (
    <div className="min-h-screen flex flex-col bg-background">
      <TopNavbar />
      
      <main className="flex-1 container px-4 sm:px-6 lg:px-8 py-6">
        <div className="flex flex-col space-y-6">
          <div className="flex flex-col space-y-1">
            <p className="text-sm font-medium text-muted-foreground">Configuration</p>
            <h1 className="text-3xl font-semibold tracking-tight">Configuration des Webhooks</h1>
          </div>
          
          {/* 1. Request Creation Webhook Card */}
          <Card className="border-l-4 border-l-blue-500">
            <CardHeader>
              <CardTitle className="flex items-center">
                <Send className="h-5 w-5 mr-2" />
                Webhook pour la Création de Demandes
              </CardTitle>
              <CardDescription>
                Ce webhook est déclenché manuellement lorsqu'une nouvelle demande est créée dans l'application.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <p className="text-sm text-muted-foreground">
                Configurez l'URL et la méthode du webhook qui sera utilisé pour notifier les systèmes externes lorsqu'une 
                nouvelle demande est créée.
              </p>
              
              <RequestFormWebhook
                webhookUrl={webhookUrl}
                webhookMethod={webhookMethod}
                onUrlChange={handleWebhookUrlChange}
                onMethodChange={handleWebhookMethodChange}
              />
              
              <Button onClick={handleSave} className="mt-4">
                <Save className="h-4 w-4 mr-2" />
                Sauvegarder
              </Button>
            </CardContent>
          </Card>
          
          {/* 2. Index Status Webhook Card */}
          <Card className="border-l-4 border-l-green-500">
            <CardHeader>
              <CardTitle className="flex items-center">
                <RefreshCw className="h-5 w-5 mr-2" />
                Webhook Automatique pour les Index
              </CardTitle>
              <CardDescription>
                Ce webhook est déclenché automatiquement lorsque les statuts d'un index atteignent "Ready for Analysis".
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <p className="text-sm text-muted-foreground">
                Configurez l'URL du webhook qui sera automatiquement déclenché lorsque les champs 'index_status' et 'doc_status' 
                de la table Index sont tous deux définis sur 'Ready for Analysis'.
              </p>
              
              <div className="space-y-4">
                <div className="grid grid-cols-1 gap-2">
                  <label htmlFor="status_webhook_url" className="text-sm font-medium">
                    URL du Webhook pour les Index
                  </label>
                  <input
                    type="text"
                    id="status_webhook_url"
                    className="w-full px-3 py-2 border rounded-md"
                    placeholder="Entrez l'URL du webhook n8n pour les index"
                    value={statusWebhookUrl}
                    onChange={handleStatusWebhookUrlChange}
                  />
                  <p className="text-xs text-muted-foreground">
                    URL actuelle: {statusWebhookUrl || "Aucune URL configurée"}
                  </p>
                </div>
              </div>
              
              <Button onClick={handleSave} className="mt-4">
                <Save className="h-4 w-4 mr-2" />
                Sauvegarder
              </Button>
            </CardContent>
          </Card>
          
          {/* 3. Actes Status Webhook Card */}
          <Card className="border-l-4 border-l-purple-500">
            <CardHeader>
              <CardTitle className="flex items-center">
                <DatabaseIcon className="h-5 w-5 mr-2" />
                Webhook Automatique pour les Actes
              </CardTitle>
              <CardDescription>
                Ce webhook est déclenché automatiquement lorsque les statuts d'un acte atteignent "Ready for Analysis".
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <p className="text-sm text-muted-foreground">
                Configurez l'URL du webhook qui sera automatiquement déclenché lorsque les champs 'acte_status' et 'doc_status' 
                de la table Actes sont tous deux définis sur 'Ready for Analysis'.
              </p>
              
              <div className="space-y-4">
                <div className="grid grid-cols-1 gap-2">
                  <label htmlFor="acte_webhook_url" className="text-sm font-medium">
                    URL du Webhook pour les Actes
                  </label>
                  <input
                    type="text"
                    id="acte_webhook_url"
                    className="w-full px-3 py-2 border rounded-md"
                    placeholder="Entrez l'URL du webhook n8n pour les actes"
                    value={acteWebhookUrl}
                    onChange={handleActeWebhookUrlChange}
                  />
                  <p className="text-xs text-muted-foreground">
                    URL actuelle: {acteWebhookUrl || "Aucune URL configurée"}
                  </p>
                </div>
              </div>
              
              <Button onClick={handleSave} className="mt-4">
                <Save className="h-4 w-4 mr-2" />
                Sauvegarder
              </Button>
            </CardContent>
          </Card>
        </div>
      </main>
      
      <footer className="border-t border-border/60 bg-card py-6">
        <div className="container flex flex-col sm:flex-row justify-between items-center px-4 sm:px-6 lg:px-8">
          <p className="text-sm text-muted-foreground">
            © 2025 Notaflow. Tous droits réservés.
          </p>
          <div className="flex items-center space-x-4 mt-4 sm:mt-0">
            <a href="#" className="text-sm text-muted-foreground hover:text-foreground transition-colors">
              Conditions d'utilisation
            </a>
            <a href="#" className="text-sm text-muted-foreground hover:text-foreground transition-colors">
              Politique de confidentialité
            </a>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default WebhookConfiguration;
