
import React, { useState, useEffect } from 'react';
import { NotaryRequest } from '@/lib/data';
import { fetchNotaryRequests } from '@/lib/supabase';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/context/AuthContext';
import DashboardHeader from '@/components/dashboard/DashboardHeader';
import StatusOverview from '@/components/dashboard/StatusOverview';
import RequestList from '@/components/dashboard/RequestList';

const Index: React.FC = () => {
  const [requests, setRequests] = useState<NotaryRequest[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const { toast } = useToast();
  const { user } = useAuth();

  useEffect(() => {
    const loadRequests = async () => {
      if (!user) {
        return;
      }
      setIsLoading(true);
      try {
        const data = await fetchNotaryRequests();
        setRequests(data);
      } catch (error) {
        console.error('Failed to load requests:', error);
        toast({
          title: 'Erreur',
          description: 'Impossible de charger les demandes. Veuillez réessayer plus tard.',
          variant: 'destructive'
        });
      } finally {
        setIsLoading(false);
      }
    };
    loadRequests();
    return () => {
      setRequests([]);
    };
  }, [toast, user]);

  return (
    <div className="min-h-screen flex flex-col bg-background">
      <main className="flex-1 container px-4 py-8">
        <div className="flex flex-col space-y-6">
          <DashboardHeader />
          <StatusOverview requests={requests} />
          <RequestList requests={requests} isLoading={isLoading} />
        </div>
      </main>
    </div>
  );
};

export default Index;
