
import React from 'react';
import { Link } from 'react-router-dom';
import NewRequestForm from '@/components/NewRequestForm';
import { ChevronLeft, FileText } from 'lucide-react';

const NewRequest: React.FC = () => {
  return (
    <div className="min-h-screen flex flex-col bg-background">
      <main className="flex-1 container max-w-3xl px-4 sm:px-6 py-8">
        <div className="flex flex-col space-y-6">
          <div className="flex items-center space-x-2 text-sm text-muted-foreground mb-2">
            <Link to="/" className="hover:text-foreground transition-colors flex items-center">
              <ChevronLeft className="h-4 w-4 mr-1" />
              Accueil
            </Link>
            <span>/</span>
            <span>Nouvelle demande</span>
          </div>
          
          <div>
            <h1 className="text-2xl font-bold mb-2 flex items-center gap-2">
              <FileText className="h-5 w-5 text-primary" />
              Nouvelle demande notariale
            </h1>
            <p className="text-muted-foreground">
              Veuillez remplir le formulaire ci-dessous avec les informations requises pour votre demande de recherche de titre.
            </p>
          </div>
          
          <NewRequestForm />
        </div>
      </main>
    </div>
  );
};

export default NewRequest;
