import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/lib/supabase';
import { ChevronLeft, BarChart3, BarChart, FileText, Star, Search, FolderOpen, Network, Download, Eye, Calendar, MapPin, User, Building, Hash, AlertTriangle, CheckCircle2, Clock, TrendingUp } from 'lucide-react';
import RequestDetailsHeader from '@/components/request-details/RequestDetailsHeader';
import RequestDetailsSummary from '@/components/request-details/RequestDetailsSummary';
import RequestResearchSummary from '@/components/request-details/RequestResearchSummary';
import RequestTabbedSection from '@/components/request-details/RequestTabbedSection';
import IndexEntriesList from '@/components/request-details/IndexEntriesList';
import RequestChatInterface from '@/components/request-details/RequestChatInterface';
import LoadingSpinner from '@/components/request-details/LoadingSpinner';
import NotFoundState from '@/components/request-details/NotFoundState';
import FloatingChat from '@/components/analysis-dashboard/FloatingChat';
import IndexDetailModal from '@/components/request-details/IndexDetailModal';
import ActeDetailModal from '@/components/request-details/ActeDetailModal';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import MermaidGraph from '@/components/ui/mermaid-graph';
import PdfViewer from '@/components/ui/pdf-viewer';

const RequestDetails: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const { toast } = useToast();
  const [request, setRequest] = useState<any | null>(null);
  const [indexEntries, setIndexEntries] = useState<any[]>([]);
  const [actesByIndex, setActesByIndex] = useState<{ [key: string]: any[] }>({});
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [showSidebar, setShowSidebar] = useState<boolean>(false);
  const [selectedSection, setSelectedSection] = useState<string>('overview');
  const [selectedPdfUrl, setSelectedPdfUrl] = useState<string | null>(null);
  const [mermaidDefinition, setMermaidDefinition] = useState<string>('');

  // Modal states for Mermaid node clicks
  const [selectedIndex, setSelectedIndex] = useState<any | null>(null);
  const [selectedActe, setSelectedActe] = useState<any | null>(null);
  const [isIndexModalOpen, setIsIndexModalOpen] = useState<boolean>(false);
  const [isActeModalOpen, setIsActeModalOpen] = useState<boolean>(false);

  // Helper to get status class for Mermaid nodes
  const getMermaidStatusClass = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'pending':
      case 'in progress':
      case 'inprogress': return 'inprogress';
      case 'completed': return 'completed';
      case 'error':
      case 'not available': return 'error';
      default: return 'inprogress'; // Default to in progress if status is unknown
    }
  };

  // Handle Mermaid node clicks
  const handleMermaidNodeClick = (nodeId: string, nodeType: 'request' | 'index' | 'acte' | 'radiated') => {
    if (nodeType === 'index') {
      // Find the index by ID
      const index = indexEntries.find(idx => `I${idx.id}` === nodeId);
      if (index) {
        setSelectedIndex(index);
        setIsIndexModalOpen(true);
      }
    } else if (nodeType === 'acte') {
      // Find the acte by ID
      const allActes = Object.values(actesByIndex).flat();
      const acte = allActes.find(act => `A${act.id}` === nodeId);
      if (acte) {
        setSelectedActe(acte);
        setIsActeModalOpen(true);
      }
    }
    // Note: request and radiated nodes don't have specific modals yet
  };

  // Function to generate the Mermaid graph definition
  const generateMermaidGraph = (_request: any, indexEntries: any[], actesByIndex: { [key: string]: any[] }) => {
    let graph = 'graph TD\n';

    // Enhanced CSS classes for better visual styling
    graph += `classDef demand fill:#F59E0B,stroke:#D97706,color:#FFFFFF,stroke-width:3px,rx:10,ry:10\n`; // Amber for demand
    graph += `classDef inprogress fill:#3B82F6,stroke:#2563EB,color:#FFFFFF,stroke-width:2px,rx:8,ry:8\n`; // Blue for in progress
    graph += `classDef completed fill:#10B981,stroke:#059669,color:#FFFFFF,stroke-width:2px,rx:8,ry:8\n`; // Emerald for completed
    graph += `classDef error fill:#EF4444,stroke:#DC2626,color:#FFFFFF,stroke-width:2px,rx:8,ry:8\n`; // Red for error
    graph += `classDef radiatedNode fill:#F87171,stroke:#EF4444,color:#FFFFFF,stroke-width:3px,stroke-dasharray:8 4\n`; // Light red dashed for radiated

    // 1. User Input / Demande Node with enhanced styling
    graph += `Request([📋 Demande]):::demand\n`;

    // 2. Index Nodes and their connections to Request
    indexEntries.forEach(index => {
      const indexStatus = index.status || 'completed';
      const indexStatusText = indexStatus === 'completed' ? '✅ Completed' : (indexStatus === 'inprogress' || indexStatus === 'in progress' ? '⏳ In Progress' : indexStatus);
      const indexStatusClass = getMermaidStatusClass(indexStatus);

      // Enhanced node content with icons and better formatting
      const nodeLabel = `📄 Index ${index.lot_number || index.doc_number || index.id.substring(0, 8)}`;
      const nodeContent = `${nodeLabel}\\n${indexStatusText}`;

      graph += `I${index.id}[${nodeContent}]:::${indexStatusClass}\n`;

      // Connection from Request to Index with improved styling
      graph += `Request -->|📁 contains| I${index.id}\n`;
    });

    // 3. Acte Nodes and their connections to Index
    Object.values(actesByIndex).flat().forEach(acte => {
      const acteStatus = acte.status || 'completed';
      const acteStatusText = acteStatus === 'completed' ? '✅ Completed' : (acteStatus === 'inprogress' || acteStatus === 'in progress' ? '⏳ In Progress' : acteStatus);
      const acteStatusClass = getMermaidStatusClass(acteStatus);

      // Enhanced node content with icons
      const nodeLabel = `📜 ${acte.acte_type || 'Acte'} ${acte.acte_publication_number || acte.doc_number || acte.id.substring(0, 8)}`;
      const nodeContent = `${nodeLabel}\\n${acteStatusText}`;

      graph += `A${acte.id}[${nodeContent}]:::${acteStatusClass}\n`;

      // Connection from parent Index to Acte (using index_id)
      if (acte.index_id) {
        graph += `I${acte.index_id} -->|📋 includes| A${acte.id}\n`;
      }

      // 4. Radiated Actes (if applicable) - Fixed syntax for diamond shape
      if (acte.is_radiated) {
        graph += `Rad${acte.id}{❌ Radié}:::radiatedNode\n`;
        graph += `A${acte.id} -.->|🚫 radiated| Rad${acte.id}\n`;
      }
    });

    return graph;
  };

  useEffect(() => {
    const fetchRequestDetails = async () => {
      setIsLoading(true);

      try {
        // Fetch the request
        const { data: requestData, error: requestError } = await supabase
          .from('requests')
          .select('*')
          .eq('id', id)
          .single();

        if (requestError) throw requestError;

        setRequest(requestData);

        // Fetch index entries
        const { data: indexData, error: indexError } = await supabase
          .from('index')
          .select('*')
          .eq('request_id', id)
          .order('created_at', { ascending: false });

        if (indexError) throw indexError;

        // Sort index entries by doc_number if it exists
        const sortedIndexData = [...indexData].sort((a, b) => {
          // Handle missing values or NaN
          if (!a.doc_number && !b.doc_number) return 0;
          if (!a.doc_number) return 1;
          if (!b.doc_number) return -1;

          return Number(a.doc_number) - Number(b.doc_number);
        });

        setIndexEntries(sortedIndexData);

        // Fetch actes for the request
        const { data: actesData, error: actesError } = await supabase
          .from('actes')
          .select('*')
          .eq('request_id', id)
          .order('created_at', { ascending: false });

        if (actesError) throw actesError;

        // Group actes by index_id
        const actesMap: { [key: string]: any[] } = {};

        if (actesData) {
          sortedIndexData.forEach(index => {
            actesMap[index.id] = actesData.filter(acte => acte.index_id === index.id) || [];
          });
        }

        setActesByIndex(actesMap);

        // Generate Mermaid graph after all data is fetched
        if (requestData && sortedIndexData && actesMap) {
          const graphDefinition = generateMermaidGraph(requestData, sortedIndexData, actesMap);
          setMermaidDefinition(graphDefinition);
        }

      } catch (error) {
        console.error('Error fetching request details:', error);
        toast({
          title: 'Erreur',
          description: 'Impossible de charger les détails de la demande. Veuillez réessayer plus tard.',
          variant: 'destructive',
        });
      } finally {
        setIsLoading(false);
      }
    };

    if (id) {
      fetchRequestDetails();
    }
  }, [id, toast]);

  if (isLoading) {
    return <LoadingSpinner />;
  }

  if (!request) {
    return <NotFoundState />;
  }

  const analysisData = {
    request,
    indexes: indexEntries,
    actes: Object.values(actesByIndex).flat()
  };

  const sidebarSections = [
    { id: 'overview', label: 'Vue d\'ensemble', icon: BarChart, completed: true },
    { id: 'research-summary', label: 'Résumé de la recherche', icon: FileText, completed: !!request.research_summary },
    { id: 'highlights', label: 'Faits saillants', icon: Star, completed: !!(request.servitudes || request.regimes_matrimoniaux || request.erreurs || request.autres_considerations) },
    { id: 'research-steps', label: 'Étapes de recherches', icon: Search, completed: indexEntries.length > 0 },
    { id: 'documents', label: 'Documents', icon: FolderOpen, completed: indexEntries.length > 0 || Object.values(actesByIndex).flat().length > 0 },
    { id: 'relations', label: 'Relations', icon: Network, completed: false }
  ];

  const renderSidebarContent = () => {
    switch (selectedSection) {
      case 'research-summary':
        return id ? <RequestResearchSummary requestId={id} /> : null;
      case 'highlights':
        return id ? <RequestTabbedSection requestId={id} /> : null;
      case 'research-steps':
        return <IndexEntriesList indexEntries={indexEntries} actesByIndex={actesByIndex} />;
      case 'documents':
        return (
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <h2 className="text-2xl font-semibold flex items-center">
                <FolderOpen className="h-6 w-6 mr-2 text-primary" />
                Documents
              </h2>
              <Button variant="outline" size="sm">
                <Download className="h-4 w-4 mr-2" />
                Télécharger tout
              </Button>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div className="bg-white dark:bg-gray-800 rounded-lg border p-6">
                <h3 className="text-lg font-semibold mb-4 flex items-center">
                  <FileText className="h-5 w-5 mr-2 text-blue-600" />
                  Index ({indexEntries.length})
                </h3>
                <div className="space-y-2 max-h-40 overflow-y-auto">
                  {indexEntries.length > 0 ? (
                    indexEntries.slice(0, 5).map((index, idx) => (
                      <div key={idx} className="flex items-center justify-between p-2 hover:bg-gray-50 dark:hover:bg-gray-700 rounded">
                        <span className="text-sm">{index.doc_number || `Document ${idx + 1}`}</span>
                        <Button variant="ghost" size="sm" onClick={() => setSelectedPdfUrl(index.file_url)}>
                          <Eye className="h-4 w-4" />
                        </Button>
                      </div>
                    ))
                  ) : (
                    <p className="text-sm text-gray-500">Aucun index trouvé</p>
                  )}
                </div>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-lg border p-6">
                <h3 className="text-lg font-semibold mb-4 flex items-center">
                  <FileText className="h-5 w-5 mr-2 text-green-600" />
                  Actes ({Object.values(actesByIndex).flat().length})
                </h3>
                <div className="space-y-2 max-h-40 overflow-y-auto">
                  {Object.values(actesByIndex).flat().length > 0 ? (
                    Object.values(actesByIndex).flat().slice(0, 5).map((acte, idx) => (
                      <div key={idx} className="flex items-center justify-between p-2 hover:bg-gray-50 dark:hover:bg-gray-700 rounded">
                        <span className="text-sm">{acte.acte_type || `Acte ${idx + 1}`}</span>
                        <Button variant="ghost" size="sm" onClick={() => setSelectedPdfUrl(acte.file_url)}>
                          <Eye className="h-4 w-4" />
                        </Button>
                      </div>
                    ))
                  ) : (
                    <p className="text-sm text-gray-500">Aucun acte trouvé</p>
                  )}
                </div>
              </div>
            </div>

            <div className="text-center py-8 bg-gray-50 dark:bg-gray-800 rounded-lg border border-dashed">
              {selectedPdfUrl ? (
                <PdfViewer fileUrl={selectedPdfUrl} />
              ) : (
                <>
                  <FolderOpen className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                    Visionneuse de documents
                  </h3>
                  <p className="text-gray-500 dark:text-gray-400 mb-4">
                    Sélectionnez un document pour le visualiser.
                  </p>
                  <Button variant="outline" disabled>
                    <Eye className="h-4 w-4 mr-2" />
                    Ouvrir la visionneuse
                  </Button>
                </>
              )}
            </div>
          </div>
        );
      case 'relations':
        return (
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <h2 className="text-2xl font-semibold flex items-center">
                <Network className="h-6 w-6 mr-2 text-primary" />
                Relations & Graphique
              </h2>
              <Button variant="outline" size="sm" disabled>
                <Download className="h-4 w-4 mr-2" />
                Exporter
              </Button>
            </div>

            <div className="bg-gray-50 dark:bg-gray-800 rounded-lg border border-dashed p-6">
              {mermaidDefinition ? (
                <MermaidGraph
                  definition={mermaidDefinition}
                  onNodeClick={handleMermaidNodeClick}
                />
              ) : (
                <div className="text-center py-6">
                  <Network className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-xl font-medium text-gray-900 dark:text-white mb-2">
                    Graphique Mermaid des Relations
                  </h3>
                  <p className="text-gray-500 dark:text-gray-400 mb-6 max-w-md mx-auto">
                    Visualisation interactive des relations entre les documents, propriétés et personnes impliquées dans cette demande.
                  </p>
                  <div className="space-y-3">
                    <Button variant="outline" disabled>
                      <Network className="h-4 w-4 mr-2" />
                      Générer le graphique
                    </Button>
                    <p className="text-xs text-gray-400">
                      Fonctionnalité en cours de développement
                    </p>
                  </div>
                </div>
              )}
            </div>

            {/* Enhanced Legend Section */}
            <div className="bg-white dark:bg-gray-800 rounded-lg border p-6 mt-6 shadow-sm">
              <h3 className="text-lg font-semibold mb-4 flex items-center">
                <span className="mr-2">🎨</span>
                Légende du graphique
              </h3>
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
                <div className="flex items-center space-x-3 p-2 rounded-lg bg-gray-50 dark:bg-gray-700">
                  <span className="w-4 h-4 rounded-lg bg-[#F59E0B] border-2 border-[#D97706]"></span>
                  <span className="text-sm font-medium text-gray-700 dark:text-gray-300">📋 Demande</span>
                </div>
                <div className="flex items-center space-x-3 p-2 rounded-lg bg-gray-50 dark:bg-gray-700">
                  <span className="w-4 h-4 rounded-lg bg-[#3B82F6] border-2 border-[#2563EB]"></span>
                  <span className="text-sm font-medium text-gray-700 dark:text-gray-300">⏳ En cours</span>
                </div>
                <div className="flex items-center space-x-3 p-2 rounded-lg bg-gray-50 dark:bg-gray-700">
                  <span className="w-4 h-4 rounded-lg bg-[#10B981] border-2 border-[#059669]"></span>
                  <span className="text-sm font-medium text-gray-700 dark:text-gray-300">✅ Complété</span>
                </div>
                <div className="flex items-center space-x-3 p-2 rounded-lg bg-gray-50 dark:bg-gray-700">
                  <span className="w-4 h-4 rounded-lg bg-[#EF4444] border-2 border-[#DC2626]"></span>
                  <span className="text-sm font-medium text-gray-700 dark:text-gray-300">❌ Erreur</span>
                </div>
                <div className="flex items-center space-x-3 p-2 rounded-lg bg-gray-50 dark:bg-gray-700">
                  <span className="w-4 h-4 border-2 border-dashed border-[#F87171] rounded-lg bg-[#F87171]/20"></span>
                  <span className="text-sm font-medium text-gray-700 dark:text-gray-300">🚫 Radié</span>
                </div>
              </div>
              <div className="mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
                <p className="text-sm text-blue-800 dark:text-blue-200 flex items-center">
                  <span className="mr-2">💡</span>
                  <strong>Astuce:</strong> Cliquez sur les nœuds du graphique pour voir les détails des documents.
                </p>
              </div>
            </div>
          </div>
        );
      default:
        const formatDate = (dateString: string) => {
          try {
            return new Date(dateString).toLocaleDateString('fr-FR', {
              year: 'numeric',
              month: 'long',
              day: 'numeric'
            });
          } catch (error) {
            return 'Date invalide';
          }
        };

        const translateStatus = (status: string): string => {
          switch (status?.toLowerCase()) {
            case 'pending': return 'En attente';
            case 'inprogress':
            case 'in progress': return 'En cours';
            case 'completed': return 'Complété';
            case 'error': return 'Erreur';
            default: return status || 'Inconnu';
          }
        };

        return (
          <div className="space-y-6">
            {/* Enhanced Header with gradient background */}
            <div className="bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-blue-950 dark:to-indigo-900 rounded-xl p-6 border border-blue-200 dark:border-blue-800">
              <div className="flex items-start justify-between">
                <div className="flex items-center space-x-4">
                  <div className="h-16 w-16 rounded-xl bg-primary/10 flex items-center justify-center">
                    <Building className="h-8 w-8 text-primary" />
                  </div>
                  <div>
                    <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                      {request?.seller_name || 'Chargement...'}
                    </h1>
                    <div className="flex items-center space-x-2 mt-2">
                      <MapPin className="h-4 w-4 text-gray-500" />
                      <p className="text-gray-600 dark:text-gray-300">
                        {request?.seller_address || ''}
                      </p>
                    </div>
                    <div className="flex items-center space-x-4 mt-3">
                      <div className="flex items-center space-x-1">
                        <Calendar className="h-4 w-4 text-gray-500" />
                        <span className="text-sm text-gray-600 dark:text-gray-400">
                          Créée le {formatDate(request?.created_at)}
                        </span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Hash className="h-4 w-4 text-gray-500" />
                        <span className="text-sm font-mono text-gray-600 dark:text-gray-400">
                          #{request?.id?.substring(0, 8)}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="text-right">
                  <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
                    request?.status === 'completed'
                      ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                      : request?.status === 'error'
                      ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                      : 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
                  }`}>
                    {request?.status === 'completed' && <CheckCircle2 className="h-4 w-4 mr-1" />}
                    {request?.status === 'error' && <AlertTriangle className="h-4 w-4 mr-1" />}
                    {!['completed', 'error'].includes(request?.status) && <Clock className="h-4 w-4 mr-1" />}
                    {translateStatus(request?.status)}
                  </div>
                  {request?.completed_at && (
                    <p className="text-xs text-gray-500 mt-2">
                      Complétée le {formatDate(request?.completed_at)}
                    </p>
                  )}
                </div>
              </div>
            </div>

            {/* Enhanced Stats Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6 hover:shadow-lg transition-shadow">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Documents totaux</p>
                    <p className="text-3xl font-bold text-gray-900 dark:text-white mt-1">
                      {indexEntries.length + Object.values(actesByIndex).flat().length}
                    </p>
                    <p className="text-xs text-gray-500 mt-1">
                      {indexEntries.length} index • {Object.values(actesByIndex).flat().length} actes
                    </p>
                  </div>
                  <div className="p-3 bg-blue-100 dark:bg-blue-900 rounded-xl">
                    <FileText className="h-8 w-8 text-blue-600 dark:text-blue-400" />
                  </div>
                </div>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6 hover:shadow-lg transition-shadow">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Analyses</p>
                    <p className="text-3xl font-bold text-gray-900 dark:text-white mt-1">
                      {[request?.servitudes, request?.regimes_matrimoniaux, request?.erreurs, request?.autres_considerations].filter(Boolean).length}/4
                    </p>
                    <p className="text-xs text-gray-500 mt-1">sections complétées</p>
                  </div>
                  <div className="p-3 bg-purple-100 dark:bg-purple-900 rounded-xl">
                    <TrendingUp className="h-8 w-8 text-purple-600 dark:text-purple-400" />
                  </div>
                </div>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6 hover:shadow-lg transition-shadow">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Servitudes</p>
                    <p className="text-3xl font-bold text-gray-900 dark:text-white mt-1">
                      {request?.servitudes ? '3' : '0'}
                    </p>
                    <p className="text-xs text-gray-500 mt-1">
                      {request?.servitudes ? 'identifiées' : 'en analyse'}
                    </p>
                  </div>
                  <div className="p-3 bg-orange-100 dark:bg-orange-900 rounded-xl">
                    <AlertTriangle className="h-8 w-8 text-orange-600 dark:text-orange-400" />
                  </div>
                </div>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6 hover:shadow-lg transition-shadow">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Alertes</p>
                    <p className="text-3xl font-bold text-gray-900 dark:text-white mt-1">
                      {request?.erreurs ? '1' : '0'}
                    </p>
                    <p className="text-xs text-gray-500 mt-1">
                      {request?.erreurs ? 'nécessite attention' : 'aucune erreur'}
                    </p>
                  </div>
                  <div className="p-3 bg-red-100 dark:bg-red-900 rounded-xl">
                    {request?.erreurs ? (
                      <AlertTriangle className="h-8 w-8 text-red-600 dark:text-red-400" />
                    ) : (
                      <CheckCircle2 className="h-8 w-8 text-green-600 dark:text-green-400" />
                    )}
                  </div>
                </div>
              </div>
            </div>

            {/* Request Information Cards */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Client Information */}
              <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6">
                <div className="flex items-center mb-4">
                  <User className="h-5 w-5 text-primary mr-2" />
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Informations client</h3>
                </div>
                <div className="space-y-3">
                  <div>
                    <label className="text-sm font-medium text-gray-500 dark:text-gray-400">Nom du vendeur</label>
                    <p className="text-gray-900 dark:text-white font-medium">{request?.seller_name || 'Non spécifié'}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500 dark:text-gray-400">Adresse de la propriété</label>
                    <p className="text-gray-900 dark:text-white">{request?.seller_address || 'Non spécifiée'}</p>
                  </div>
                  {request?.property_details && (
                    <div>
                      <label className="text-sm font-medium text-gray-500 dark:text-gray-400">Détails de la propriété</label>
                      <p className="text-gray-900 dark:text-white">{request.property_details}</p>
                    </div>
                  )}
                </div>
              </div>

              {/* Research Summary */}
              <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6">
                <div className="flex items-center mb-4">
                  <Search className="h-5 w-5 text-primary mr-2" />
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Résumé de recherche</h3>
                </div>
                {request?.research_summary ? (
                  <div className="prose prose-sm dark:prose-invert max-w-none">
                    <p className="text-gray-700 dark:text-gray-300 text-sm leading-relaxed">
                      {request.research_summary.length > 300
                        ? `${request.research_summary.substring(0, 300)}...`
                        : request.research_summary
                      }
                    </p>
                  </div>
                ) : (
                  <p className="text-gray-500 dark:text-gray-400 text-sm italic">
                    Le résumé de recherche sera généré automatiquement une fois l'analyse terminée.
                  </p>
                )}
              </div>
            </div>

            {/* Detailed Analysis Status */}
            <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6">
              <div className="flex items-center mb-6">
                <BarChart className="h-5 w-5 text-primary mr-2" />
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">État détaillé des analyses</h3>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div className="border border-gray-200 dark:border-gray-600 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Servitudes</span>
                    {request?.servitudes ? (
                      <CheckCircle2 className="h-4 w-4 text-green-500" />
                    ) : (
                      <Clock className="h-4 w-4 text-yellow-500" />
                    )}
                  </div>
                  <span className={`inline-flex px-2 py-1 rounded-full text-xs font-medium ${
                    request?.servitudes
                      ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                      : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
                  }`}>
                    {request?.servitudes ? 'Complété' : 'À vérifier'}
                  </span>
                </div>

                <div className="border border-gray-200 dark:border-gray-600 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Régimes matrimoniaux</span>
                    {request?.regimes_matrimoniaux ? (
                      <CheckCircle2 className="h-4 w-4 text-green-500" />
                    ) : (
                      <Clock className="h-4 w-4 text-yellow-500" />
                    )}
                  </div>
                  <span className={`inline-flex px-2 py-1 rounded-full text-xs font-medium ${
                    request?.regimes_matrimoniaux
                      ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                      : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
                  }`}>
                    {request?.regimes_matrimoniaux ? 'Analysé' : 'En cours'}
                  </span>
                </div>

                <div className="border border-gray-200 dark:border-gray-600 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Erreurs identifiées</span>
                    {request?.erreurs ? (
                      <AlertTriangle className="h-4 w-4 text-red-500" />
                    ) : (
                      <CheckCircle2 className="h-4 w-4 text-green-500" />
                    )}
                  </div>
                  <span className={`inline-flex px-2 py-1 rounded-full text-xs font-medium ${
                    request?.erreurs
                      ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                      : 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                  }`}>
                    {request?.erreurs ? 'Trouvées' : 'Aucune'}
                  </span>
                </div>

                <div className="border border-gray-200 dark:border-gray-600 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Autres considérations</span>
                    {request?.autres_considerations ? (
                      <CheckCircle2 className="h-4 w-4 text-green-500" />
                    ) : (
                      <Clock className="h-4 w-4 text-yellow-500" />
                    )}
                  </div>
                  <span className={`inline-flex px-2 py-1 rounded-full text-xs font-medium ${
                    request?.autres_considerations
                      ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                      : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
                  }`}>
                    {request?.autres_considerations ? 'Complété' : 'En cours'}
                  </span>
                </div>
              </div>
            </div>
          </div>
        );
    }
  };

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-6 max-w-7xl">
        {/* Header Navigation */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-4">
            <Link
              to="/"
              className="inline-flex items-center text-sm font-medium text-muted-foreground hover:text-foreground transition-colors"
            >
              <ChevronLeft className="mr-2 h-4 w-4" />
              Tableau de bord
            </Link>
            <span className="text-muted-foreground">•</span>
            <span className="text-sm font-medium">Détails de la demande</span>
          </div>

          <Button
            variant={showSidebar ? "default" : "outline"}
            onClick={() => setShowSidebar(!showSidebar)}
            className="flex items-center space-x-2"
          >
            <BarChart3 className="h-4 w-4" />
            <span>{showSidebar ? 'Vue classique' : 'Vue analytique'}</span>
          </Button>
        </div>

        {showSidebar ? (
          /* Sidebar Layout */
          <div className="flex gap-6">
            {/* Sidebar - Fixed/Sticky */}
            <div className="w-72 sticky top-20 h-fit ml-2">
              <Card>
                <CardContent className="p-3">
                  <h3 className="font-semibold mb-3 text-xs uppercase tracking-wide text-muted-foreground">
                    Analyses
                  </h3>
                  <div className="space-y-1">
                    {sidebarSections.map((section) => {
                      const IconComponent = section.icon;
                      return (
                        <button
                          key={section.id}
                          onClick={() => setSelectedSection(section.id)}
                          className={`w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors ${
                            selectedSection === section.id
                              ? 'bg-primary/10 text-primary border border-primary/20'
                              : 'hover:bg-muted text-muted-foreground hover:text-foreground'
                          }`}
                        >
                          <IconComponent className="h-4 w-4" />
                          <div className="flex-1">
                            <span className="text-sm font-medium">{section.label}</span>
                          </div>
                        </button>
                      );
                    })}
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Main Content */}
            <div className="flex-1">
              <Card>
                <CardContent className="p-6">
                  {renderSidebarContent()}
                </CardContent>
              </Card>
            </div>
          </div>
        ) : (
          /* Original Classic Layout */
          <div className="space-y-6">
            <RequestDetailsHeader request={request} />
            <RequestDetailsSummary
              request={request}
              indexCount={indexEntries.length}
              acteCount={Object.values(actesByIndex).flat().length}
            />
            {id && <RequestResearchSummary requestId={id} />}
            {id && <RequestTabbedSection requestId={id} />}
            <IndexEntriesList indexEntries={indexEntries} actesByIndex={actesByIndex} />
            {id && (
              <Card>
                <CardContent className="p-6">
                  <RequestChatInterface requestId={id} />
                </CardContent>
              </Card>
            )}
          </div>
        )}
      </div>

      {/* Floating Chat - Always present */}
      {id && (
        <FloatingChat
          requestId={id}
          currentSection={selectedSection}
          contextData={analysisData}
        />
      )}

      {/* Modals for Mermaid node clicks */}
      {selectedIndex && (
        <IndexDetailModal
          index={selectedIndex}
          open={isIndexModalOpen}
          onOpenChange={setIsIndexModalOpen}
        />
      )}

      {selectedActe && (
        <ActeDetailModal
          acte={selectedActe}
          open={isActeModalOpen}
          onOpenChange={setIsActeModalOpen}
        />
      )}
    </div>
  );
};

export default RequestDetails;
