
import React, { useState, useEffect } from 'react';
import { useNavigate, Link, useLocation } from 'react-router-dom';
import { useAuth } from '@/context/AuthContext';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { MailCheck, KeyRound, Loader2, UserRound } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { supabase } from '@/lib/supabase';

const Auth = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { toast } = useToast();
  const { user, profile, signIn, signUp, isLoading } = useAuth();
  const [loading, setLoading] = useState(false);

  // Check if we're coming back from a password reset
  const searchParams = new URLSearchParams(location.search);
  const hasResetParam = searchParams.get('resetPassword') === 'true';
  const [showResetDialog, setShowResetDialog] = useState(false);
  const [newPassword, setNewPassword] = useState('');
  const [resettingPassword, setResettingPassword] = useState(false);

  // Login form state
  const [loginEmail, setLoginEmail] = useState('');
  const [loginPassword, setLoginPassword] = useState('');

  // Signup form state
  const [signupEmail, setSignupEmail] = useState('');
  const [signupPassword, setSignupPassword] = useState('');
  const [signupFullName, setSignupFullName] = useState('');

  // Check if coming back from a password reset
  useEffect(() => {
    if (hasResetParam) {
      setShowResetDialog(true);
    }
  }, [hasResetParam]);

  // Redirect if already logged in and approved
  useEffect(() => {
    if (user && profile && !isLoading) {
      if (profile.is_approved) {
        const from = location.state?.from?.pathname || '/';
        navigate(from, { replace: true });
      }
    }
  }, [user, profile, isLoading, navigate, location]);
  
  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    try {
      const { error } = await signIn(loginEmail, loginPassword);
      if (error) {
        console.error('Login error:', error);
        toast({
          title: 'Erreur de connexion',
          description: error.message || 'Vérifiez vos identifiants et réessayez.',
          variant: 'destructive'
        });
      } else {
        toast({
          title: 'Connexion réussie',
          description: 'Bienvenue sur NotaireQC!'
        });
      }
    } finally {
      setLoading(false);
    }
  };

  const handleSignup = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    try {
      const { error } = await signUp(signupEmail, signupPassword, { full_name: signupFullName });
      if (error) {
        console.error('Signup error:', error);
        toast({
          title: 'Erreur d\'inscription',
          description: error.message || 'Une erreur est survenue lors de l\'inscription.',
          variant: 'destructive'
        });
      } else {
        toast({
          title: 'Inscription réussie',
          description: 'Votre compte a été créé avec succès. Veuillez attendre l\'approbation de l\'administrateur.'
        });
      }
    } finally {
      setLoading(false);
    }
  };

  const handlePasswordReset = async () => {
    if (!newPassword || newPassword.length < 6) {
      toast({
        title: 'Erreur',
        description: 'Le mot de passe doit comporter au moins 6 caractères.',
        variant: 'destructive'
      });
      return;
    }

    setResettingPassword(true);
    try {
      const { error } = await supabase.auth.updateUser({
        password: newPassword
      });

      if (error) {
        toast({
          title: 'Erreur',
          description: error.message || 'Impossible de réinitialiser le mot de passe.',
          variant: 'destructive'
        });
      } else {
        toast({
          title: 'Succès',
          description: 'Votre mot de passe a été réinitialisé avec succès.'
        });
        setShowResetDialog(false);
        
        // Remove the resetPassword query param
        const searchParams = new URLSearchParams(location.search);
        searchParams.delete('resetPassword');
        navigate({
          pathname: location.pathname,
          search: searchParams.toString()
        });
      }
    } catch (error) {
      console.error('Error resetting password:', error);
      toast({
        title: 'Erreur',
        description: 'Une erreur inattendue s\'est produite.',
        variant: 'destructive'
      });
    } finally {
      setResettingPassword(false);
    }
  };

  // If logged in but not approved, show approval pending message
  if (user && profile && !profile.is_approved) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-background p-4">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle>Compte en attente d'approbation</CardTitle>
            <CardDescription>Votre compte est en cours de vérification.</CardDescription>
          </CardHeader>
          <CardContent>
            <Alert>
              <AlertDescription>
                Votre compte est en attente d'approbation. Contactez <EMAIL> au besoin.
              </AlertDescription>
            </Alert>
          </CardContent>
          <CardFooter>
            <Button onClick={() => navigate('/')} className="w-full">
              Retour à l'accueil
            </Button>
          </CardFooter>
        </Card>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  return (
    <div className="flex items-center justify-center min-h-screen bg-background p-4">
      <div className="w-full max-w-md">
        <div className="text-center mb-8">
          <Link to="/" className="inline-block">
            <div className="flex items-center justify-center mb-2">
              <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-primary via-blue-400 to-blue-600 flex items-center justify-center text-primary-foreground font-bold text-xl shadow-lg">
                N
              </div>
            </div>
            <h1 className="text-2xl font-bold tracking-tight gradient-text">Notaflow</h1>
          </Link>
          <p className="text-sm text-muted-foreground mt-2">
            Plateforme de gestion de recherches notariales au Québec
            Un outil par ParAito.
          </p>
        </div>
      
        <Tabs defaultValue="login" className="w-full">
          <TabsList className="grid w-full grid-cols-2 mb-6">
            <TabsTrigger value="login">Connexion</TabsTrigger>
            <TabsTrigger value="signup">Inscription</TabsTrigger>
          </TabsList>
          
          <TabsContent value="login">
            <Card>
              <CardHeader>
                <CardTitle>Connexion</CardTitle>
                <CardDescription>Connectez-vous à votre compte pour accéder à vos recherches.</CardDescription>
              </CardHeader>
              <form onSubmit={handleLogin}>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="email">Adresse courriel</Label>
                    <div className="relative">
                      <Input id="email" type="email" placeholder="<EMAIL>" value={loginEmail} onChange={e => setLoginEmail(e.target.value)} required className="pl-10" />
                      <MailCheck className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <Label htmlFor="password">Mot de passe</Label>
                      <Link to="/reset-password" className="text-xs text-primary hover:underline">
                        Mot de passe oublié?
                      </Link>
                    </div>
                    <div className="relative">
                      <Input id="password" type="password" value={loginPassword} onChange={e => setLoginPassword(e.target.value)} required className="pl-10" />
                      <KeyRound className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                    </div>
                  </div>
                </CardContent>
                
                <CardFooter>
                  <Button type="submit" className="w-full" disabled={loading}>
                    {loading ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Connexion en cours...
                      </>
                    ) : 'Se connecter'}
                  </Button>
                </CardFooter>
              </form>
            </Card>
          </TabsContent>
          
          <TabsContent value="signup">
            <Card>
              <CardHeader>
                <CardTitle>Inscription</CardTitle>
                <CardDescription>Créez un compte pour accéder à l'application.</CardDescription>
              </CardHeader>
              <form onSubmit={handleSignup}>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="fullName">Nom complet</Label>
                    <div className="relative">
                      <Input id="fullName" type="text" placeholder="Prénom Nom" value={signupFullName} onChange={e => setSignupFullName(e.target.value)} required className="pl-10" />
                      <UserRound className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="signupEmail">Adresse courriel</Label>
                    <div className="relative">
                      <Input id="signupEmail" type="email" placeholder="<EMAIL>" value={signupEmail} onChange={e => setSignupEmail(e.target.value)} required className="pl-10" />
                      <MailCheck className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="signupPassword">Mot de passe</Label>
                    <div className="relative">
                      <Input id="signupPassword" type="password" value={signupPassword} onChange={e => setSignupPassword(e.target.value)} required className="pl-10" />
                      <KeyRound className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                    </div>
                  </div>
                </CardContent>
                
                <CardFooter>
                  <Button type="submit" className="w-full" disabled={loading}>
                    {loading ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Inscription en cours...
                      </>
                    ) : 'S\'inscrire'}
                  </Button>
                </CardFooter>
              </form>
            </Card>
          </TabsContent>
        </Tabs>
        
        <div className="text-center mt-6 text-sm text-muted-foreground">
          
        </div>
      </div>

      {/* Password Reset Dialog */}
      <Dialog open={showResetDialog} onOpenChange={setShowResetDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Définir un nouveau mot de passe</DialogTitle>
            <DialogDescription>
              Veuillez saisir votre nouveau mot de passe ci-dessous.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="newPassword">Nouveau mot de passe</Label>
              <div className="relative">
                <Input 
                  id="newPassword" 
                  type="password" 
                  value={newPassword} 
                  onChange={e => setNewPassword(e.target.value)}
                  placeholder="Minimum 6 caractères"
                  className="pl-10"
                />
                <KeyRound className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button type="submit" onClick={handlePasswordReset} disabled={resettingPassword}>
              {resettingPassword ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Réinitialisation...
                </>
              ) : 'Réinitialiser le mot de passe'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default Auth;
