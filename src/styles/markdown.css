
.markdown-content {
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.markdown-content p {
  margin-bottom: 0.5rem;
}

.markdown-content p:last-child {
  margin-bottom: 0;
}

.markdown-content ul {
  list-style-type: disc;
  padding-left: 1.5rem;
  margin-bottom: 0.5rem;
}

.markdown-content ol {
  list-style-type: decimal;
  padding-left: 1.5rem;
  margin-bottom: 0.5rem;
}

.markdown-content a {
  color: #0077cc;
  text-decoration: underline;
}

.markdown-content h1, 
.markdown-content h2, 
.markdown-content h3, 
.markdown-content h4, 
.markdown-content h5, 
.markdown-content h6 {
  margin-top: 1rem;
  margin-bottom: 0.5rem;
  font-weight: 600;
}

.markdown-content pre {
  background-color: #f5f5f5;
  padding: 0.5rem;
  border-radius: 0.25rem;
  overflow-x: auto;
}

.markdown-content code {
  background-color: #f5f5f5;
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-family: monospace;
}

.markdown-content blockquote {
  border-left: 3px solid #e2e8f0;
  padding-left: 1rem;
  margin-left: 0;
  color: #64748b;
}

.markdown-content hr {
  margin: 1rem 0;
  border: 0;
  border-top: 1px solid #e2e8f0;
}
