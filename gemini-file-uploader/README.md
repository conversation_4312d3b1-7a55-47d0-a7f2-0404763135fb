# Gemini File Uploader

A self-contained Node.js script that efficiently uploads large files to the Google Gemini Document Processing API. This script is designed to be invoked by external orchestration tools like n8n.

## Features

- Efficient streaming upload for large files using `fs.createReadStream()`
- Robust error handling with informative error messages
- JSON output format for easy integration with orchestration tools
- Environment variable security for API keys
- Command-line interface for easy automation

## Prerequisites

- Node.js 18+ 
- Google API Key for Gemini API access

## Setup

1. Navigate to the project directory:
   ```bash
   cd gemini-file-uploader
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. **Set up your Google API Key** (see detailed instructions below)

## Google API Key Setup

### Getting Your API Key

1. Go to the [Google AI Studio](https://aistudio.google.com/app/apikey)
2. Sign in with your Google account
3. Click "Create API Key"
4. Copy the generated API key (it will look like: `AIzaSyC-abcdefghijklmnopqrstuvwxyz123456789`)

### Setting the Environment Variable

The `GOOGLE_API_KEY` environment variable can be set in several ways depending on your use case:

#### Option 1: Terminal/Command Line (Temporary - for testing)
```bash
# macOS/Linux
export GOOGLE_API_KEY="AIzaSyC-your-actual-api-key-here"

# Windows Command Prompt
set GOOGLE_API_KEY=AIzaSyC-your-actual-api-key-here

# Windows PowerShell
$env:GOOGLE_API_KEY="AIzaSyC-your-actual-api-key-here"
```

#### Option 2: Shell Profile (Permanent for your user)
Add to your shell profile file (`~/.bashrc`, `~/.zshrc`, etc.):
```bash
echo 'export GOOGLE_API_KEY="AIzaSyC-your-actual-api-key-here"' >> ~/.zshrc
source ~/.zshrc
```

#### Option 3: .env File (For development)
Create a `.env` file in your project directory:
```bash
GOOGLE_API_KEY=AIzaSyC-your-actual-api-key-here
```
Then load it before running:
```bash
source .env
node uploadGeminiFile.js /path/to/file.pdf application/pdf
```

#### Option 4: n8n Environment Variables (For production with n8n)

**Method A: n8n Server Environment**
Set the environment variable where your n8n server runs:
```bash
# In your n8n server startup script or docker-compose
GOOGLE_API_KEY=AIzaSyC-your-actual-api-key-here
```

**Method B: n8n Execute Command Node Environment**
In your n8n Execute Command node, you can set environment variables directly:
1. Open your Execute Command node
2. In the "Environment" section, add:
   - **Name**: `GOOGLE_API_KEY`
   - **Value**: `AIzaSyC-your-actual-api-key-here`
3. Your command: `node /path/to/uploadGeminiFile.js /path/to/file.pdf application/pdf`

**Method C: n8n Credentials (Recommended for security)**
1. In n8n, go to Settings → Credentials
2. Create a new credential of type "Generic Credential"
3. Add field: `api_key` with your Google API key
4. In your Execute Command node, reference it: `GOOGLE_API_KEY={{$credentials.your_credential_name.api_key}}`

### Verifying the Setup
Test that your environment variable is set correctly:
```bash
# macOS/Linux
echo $GOOGLE_API_KEY

# Windows Command Prompt
echo %GOOGLE_API_KEY%

# Windows PowerShell
echo $env:GOOGLE_API_KEY
```

## Usage

### Basic Usage

```bash
node uploadGeminiFile.js <filePath> <mimeType>
```

### Examples

Upload a PDF document:
```bash
node uploadGeminiFile.js /tmp/document.pdf application/pdf
```

Upload an image:
```bash
node uploadGeminiFile.js /tmp/image.jpg image/jpeg
```

Upload a text file:
```bash
node uploadGeminiFile.js /tmp/document.txt text/plain
```

### Command Line Arguments

1. **filePath** (required): Absolute path to the local file to upload
2. **mimeType** (required): MIME type of the file (e.g., `application/pdf`, `image/jpeg`, `text/plain`)

### Environment Variables

- **GOOGLE_API_KEY** (required): Your Google API key for accessing the Gemini API

## Output Format

### Successful Upload

The script outputs a JSON object to stdout with the following structure:

```json
{
  "geminiFileName": "files/1234567890abcdef",
  "mimeType": "application/pdf",
  "sizeBytes": 1024000
}
```

- `geminiFileName`: The Gemini-specific URI/ID for the uploaded file
- `mimeType`: The MIME type of the uploaded file
- `sizeBytes`: Size of the file in bytes

### Error Handling

- All error messages are sent to stderr
- The script exits with code 1 on any error
- The script exits with code 0 on successful upload

## Integration with n8n

This script is designed to work with n8n's "Execute Command" node:

1. **Pre-requisites**: Ensure the file is already saved locally and the `GOOGLE_API_KEY` environment variable is set
2. **Command**: `node /path/to/uploadGeminiFile.js /path/to/file.pdf application/pdf`
3. **Output**: Parse the JSON output from stdout to get the `geminiFileName`
4. **Error Handling**: Check the exit code; non-zero indicates failure

### Example n8n Execute Command Node Configuration

**Command:**
```bash
node /absolute/path/to/gemini-file-uploader/uploadGeminiFile.js {{ $json.filePath }} {{ $json.mimeType }}
```

**Environment Variables:**
- Name: `GOOGLE_API_KEY`
- Value: `{{$credentials.google_api.api_key}}` (if using n8n credentials)

**Expected Input:**
```json
{
  "filePath": "/tmp/uploaded_document.pdf",
  "mimeType": "application/pdf"
}
```

**Expected Output:**
```json
{
  "geminiFileName": "files/abc123def456",
  "mimeType": "application/pdf", 
  "sizeBytes": 1024000
}
```

### Example n8n Workflow Steps

1. **Download/Save File**: Use other n8n nodes to download and save the file locally
2. **Upload to Gemini**: Use Execute Command node with this script
3. **Process Result**: Parse the JSON output to extract `geminiFileName`
4. **Analyze with Gemini**: Use the `geminiFileName` in subsequent API calls
5. **Cleanup**: Delete the local file after processing

## Common MIME Types

- PDF: `application/pdf`
- Word Document: `application/vnd.openxmlformats-officedocument.wordprocessingml.document`
- Text: `text/plain`
- JPEG Image: `image/jpeg`
- PNG Image: `image/png`
- JSON: `application/json`
- CSV: `text/csv`

## Troubleshooting

### Common Errors

1. **"GOOGLE_API_KEY environment variable is not set"**
   - Solution: Follow the "Google API Key Setup" section above
   - Verify the variable is set: `echo $GOOGLE_API_KEY`

2. **"File not found or not accessible"**
   - Solution: Check that the file path is correct and the file exists
   - Ensure the script has read permissions for the file

3. **"Failed to upload file to Gemini API"**
   - Solution: Check your API key permissions and network connectivity
   - Verify the MIME type is supported by Gemini API
   - Check if your API key has sufficient quota/credits

4. **API Key Format Issues**
   - Ensure your API key starts with `AIzaSy` and is the full key
   - Remove any extra quotes or spaces around the key
   - Don't include `Bearer` or other prefixes

### Debug Mode

For additional debugging information, you can examine the error messages in stderr which include:
- HTTP status codes for API errors
- Detailed error responses from the Gemini API
- Stack traces for unexpected errors

## Security Notes

- The Google API key is only accessed via environment variables
- No sensitive information is logged to stdout or stderr
- The script does not store or cache any data locally
- Never commit API keys to version control
- Use n8n credentials management for production deployments

## License

MIT License
n